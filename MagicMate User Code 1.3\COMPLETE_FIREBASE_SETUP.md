# 🔥 COMPLETE FIREBASE SETUP - All Required Functionality

## 📋 **FIREBASE PROJECT DETAILS**

### **Project Information:**
```
Project ID: linkinblink-f544a
Project Number: ************
Firebase Console: https://console.firebase.google.com/project/linkinblink-f544a
```

### **App Configurations:**
```
Web App ID: 1:************:web:a9e5c8f4c2b8e9a9e5c8f4
Android App ID: 1:************:android:a9e5c8f4c2b8e9a9e5c8f4
iOS App ID: 1:************:ios:a9e5c8f4c2b8e9a9e5c8f4
```

## 🗄️ **REQUIRED FIRESTORE COLLECTIONS**

### **1. MagicUser Collection**
**Purpose**: Store user profiles from mobile app
**Document ID**: User ID from tbl_user table
```json
{
  "uid": "user_id_from_database",
  "name": "User Name",
  "email": "<EMAIL>",
  "mobile": "+************",
  "profilePic": "profile_image_url",
  "fcmToken": "firebase_messaging_token",
  "isOnline": true,
  "lastActive": "timestamp",
  "createdAt": "timestamp",
  "appType": "event_user"
}
```

### **2. MagicOrganizer Collection**
**Purpose**: Store organizer profiles from organizer app
**Document ID**: Organizer ID from tbl_sponsore table
```json
{
  "uid": "organizer_id_from_database",
  "name": "Organizer Name",
  "email": "<EMAIL>",
  "mobile": "+************",
  "profilePic": "profile_image_url",
  "fcmToken": "firebase_messaging_token",
  "isOnline": false,
  "lastActive": "timestamp",
  "createdAt": "timestamp",
  "appType": "event_organizer"
}
```

### **3. Magic_Organization_rooms Collection**
**Purpose**: Chat system between users and organizers
**Document ID**: Conversation ID (U{user_id}_O{organizer_id})
```json
{
  "uid": "conversation_id",
  "name": "User Name",
  "email": "<EMAIL>",
  "image": "profile_image_url",
  "isOnline": true,
  "lastActive": "timestamp",
  "participants": ["user_id", "organizer_id"],
  "lastMessage": "last_message_content",
  "lastMessageTime": "timestamp"
}
```

### **4. Magic_Organization_rooms/{conversation_id}/messages Subcollection**
**Purpose**: Store chat messages
```json
{
  "content": "message_content",
  "senderId": "sender_id",
  "receiverId": "receiver_id",
  "sentTime": "timestamp",
  "messageType": "text",
  "userType": "US" // US for User, OR for Organizer
}
```

### **5. events Collection**
**Purpose**: Real-time event data sync
**Document ID**: Event ID from tbl_event table
```json
{
  "eventId": "event_id_from_database",
  "title": "Event Title",
  "description": "Event Description",
  "organizerId": "organizer_id",
  "startDate": "timestamp",
  "endDate": "timestamp",
  "location": "event_location",
  "price": 100,
  "availableTickets": 50,
  "totalTickets": 100,
  "status": "active",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### **6. bookings Collection**
**Purpose**: Real-time booking status
**Document ID**: Booking ID from tbl_booking table
```json
{
  "bookingId": "booking_id_from_database",
  "userId": "user_id",
  "eventId": "event_id",
  "organizerId": "organizer_id",
  "ticketCount": 2,
  "totalAmount": 200,
  "bookingStatus": "confirmed",
  "paymentStatus": "paid",
  "bookingDate": "timestamp",
  "eventDate": "timestamp"
}
```

### **7. hotels Collection (Merged App)**
**Purpose**: Hotel data for merged event+hotel app
**Document ID**: Hotel ID
```json
{
  "hotelId": "hotel_id",
  "name": "Hotel Name",
  "description": "Hotel Description",
  "location": "hotel_location",
  "rating": 4.5,
  "amenities": ["wifi", "parking", "pool"],
  "images": ["image_url_1", "image_url_2"],
  "pricePerNight": 2000,
  "availableRooms": 10,
  "totalRooms": 20,
  "status": "active"
}
```

### **8. hotel_bookings Collection (Merged App)**
**Purpose**: Hotel booking data
**Document ID**: Hotel booking ID
```json
{
  "bookingId": "hotel_booking_id",
  "userId": "user_id",
  "hotelId": "hotel_id",
  "checkInDate": "timestamp",
  "checkOutDate": "timestamp",
  "roomCount": 1,
  "guestCount": 2,
  "totalAmount": 4000,
  "bookingStatus": "confirmed",
  "paymentStatus": "paid"
}
```

### **9. notifications Collection**
**Purpose**: Push notification history
**Document ID**: Auto-generated
```json
{
  "userId": "user_id",
  "title": "Notification Title",
  "body": "Notification Body",
  "type": "event_booking", // event_booking, hotel_booking, chat, general
  "data": {
    "eventId": "event_id",
    "bookingId": "booking_id"
  },
  "sentAt": "timestamp",
  "readAt": "timestamp",
  "status": "sent"
}
```

## 🔐 **FIRESTORE SECURITY RULES**

### **Copy these rules to Firebase Console → Firestore → Rules:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // MagicUser collection - users can read/write their own data
    match /MagicUser/{userId} {
      allow read, write: if request.auth != null;
    }
    
    // MagicOrganizer collection - organizers can read/write their own data
    match /MagicOrganizer/{organizerId} {
      allow read, write: if request.auth != null;
    }
    
    // Magic_Organization_rooms - chat rooms
    match /Magic_Organization_rooms/{roomId} {
      allow read, write: if request.auth != null;
      
      // Messages subcollection
      match /messages/{messageId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // Events collection - read for all, write for organizers
    match /events/{eventId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // Bookings collection - users can read/write their own bookings
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null;
    }
    
    // Hotels collection - read for all, write for admins
    match /hotels/{hotelId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // Hotel bookings collection
    match /hotel_bookings/{bookingId} {
      allow read, write: if request.auth != null;
    }
    
    // Notifications collection
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 📱 **FIREBASE CLOUD MESSAGING (FCM) SETUP**

### **1. Enable Cloud Messaging:**
```
Firebase Console → Project Settings → Cloud Messaging
- Enable Cloud Messaging API
- Generate Server Key (for backend notifications)
```

### **2. FCM Server Key Configuration:**
**Add to your backend configuration:**
```php
// In your admin panel settings or config file
$fcm_server_key = "YOUR_FCM_SERVER_KEY_HERE";
$fcm_sender_id = "************";
```

### **3. Notification Types:**
```
- Event booking confirmations
- Hotel booking confirmations  
- Chat messages
- Event reminders
- Payment confirmations
- General announcements
```

## 🔧 **FIREBASE STORAGE SETUP**

### **1. Enable Firebase Storage:**
```
Firebase Console → Storage → Get Started
- Choose production mode
- Select storage location (asia-south1 recommended for India)
```

### **2. Storage Structure:**
```
/users/{user_id}/
  - profile_pictures/
  - documents/

/events/{event_id}/
  - images/
  - documents/

/hotels/{hotel_id}/
  - images/
  - documents/

/chat/{conversation_id}/
  - images/
  - files/
```

### **3. Storage Security Rules:**
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 🔑 **FIREBASE AUTHENTICATION SETUP**

### **1. Enable Authentication Methods:**
```
Firebase Console → Authentication → Sign-in method
- Enable Email/Password
- Enable Phone (for OTP)
- Enable Anonymous (optional)
```

### **2. Authentication Flow:**
```
1. User registers in mobile app
2. User data saved to MySQL database
3. Firebase user created with same UID
4. User profile saved to Firestore
5. FCM token registered for notifications
```

## 🌐 **FIREBASE HOSTING (Optional)**

### **1. Enable Hosting:**
```
Firebase Console → Hosting → Get Started
- Can host admin panel web interface
- Can host API documentation
```

### **2. Deploy Commands:**
```bash
npm install -g firebase-tools
firebase login
firebase init hosting
firebase deploy
```

## 📊 **FIREBASE ANALYTICS SETUP**

### **1. Enable Analytics:**
```
Firebase Console → Analytics → Enable
- Track user engagement
- Monitor app performance
- Track conversion events
```

### **2. Custom Events to Track:**
```
- user_registration
- event_booking
- hotel_booking
- payment_completed
- chat_message_sent
- app_opened
- feature_used
```

## 🔧 **FIREBASE FUNCTIONS (Optional)**

### **1. Cloud Functions for:**
```
- Automated notifications
- Data synchronization
- Payment processing
- Image processing
- Scheduled tasks
```

### **2. Example Function:**
```javascript
exports.sendBookingNotification = functions.firestore
  .document('bookings/{bookingId}')
  .onCreate(async (snap, context) => {
    const booking = snap.data();
    // Send notification to user and organizer
  });
```

## 🎯 **DEPLOYMENT CHECKLIST**

### **✅ Firebase Console Setup:**
- [ ] Project created: linkinblink-f544a
- [ ] Firestore enabled with collections
- [ ] Security rules configured
- [ ] Cloud Messaging enabled
- [ ] Storage enabled with rules
- [ ] Authentication methods enabled
- [ ] Analytics enabled (optional)

### **✅ Mobile App Configuration:**
- [ ] firebase_options.dart updated
- [ ] FCM tokens being generated
- [ ] User profiles syncing to Firestore
- [ ] Chat functionality working
- [ ] Push notifications working

### **✅ Backend Integration:**
- [ ] FCM server key configured
- [ ] Notification sending working
- [ ] Data sync between MySQL and Firestore
- [ ] Real-time updates working

## 📞 **FIREBASE PROJECT ACCESS**

### **Project Details:**
```
Project ID: linkinblink-f544a
Console URL: https://console.firebase.google.com/project/linkinblink-f544a
Account: <EMAIL>
```

### **Required Permissions:**
```
- Firestore Database: Read/Write
- Cloud Messaging: Send notifications
- Storage: Upload/Download files
- Authentication: Manage users
- Analytics: View reports
```

**All Firebase functionality is now documented and ready for implementation!** 🔥📱✅
