{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M18 13h3v2h-3zm-6-1v2h-2v4h2v2h5v-8z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M5 11h7V4H4v2h6v3H3v8h6v-2H5zm13 6h3v2h-3z\"\n}, \"1\")], 'ElectricalServicesSharp');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["G:/linkinblink/hotel-dashboard/node_modules/@mui/icons-material/esm/ElectricalServicesSharp.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M18 13h3v2h-3zm-6-1v2h-2v4h2v2h5v-8z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M5 11h7V4H4v2h6v3H3v8h6v-2H5zm13 6h3v2h-3z\"\n}, \"1\")], 'ElectricalServicesSharp');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,yBAAyB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}