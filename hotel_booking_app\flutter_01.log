Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter run -d chrome

## exception

ProcessException: ProcessException: Insufficient system resources exist to complete the requested service.

  Command: C:\WINDOWS\system32\reg.EXE query HKEY_CURRENT_USER\Software\Google\Chrome\BLBeacon /v version

```
#0      _ProcessImpl._start (dart:io-patch/process_patch.dart:402:33)
#1      Process.start (dart:io-patch/process_patch.dart:38:20)
#2      _runNonInteractiveProcess (dart:io-patch/process_patch.dart:579:18)
#3      Process.run (dart:io-patch/process_patch.dart:49:12)
#4      LocalProcessManager.run (package:process/src/interface/local_process_manager.dart:72:22)
#5      ErrorHandlingProcessManager.run.<anonymous closure> (package:flutter_tools/src/base/error_handling_io.dart:674:24)
#6      _run (package:flutter_tools/src/base/error_handling_io.dart:569:20)
#7      ErrorHandlingProcessManager.run (package:flutter_tools/src/base/error_handling_io.dart:673:12)
#8      GoogleChromeDevice._computeSdkNameAndVersion (package:flutter_tools/src/web/web_device.dart:220:60)
#9      GoogleChromeDevice.sdkNameAndVersion (package:flutter_tools/src/web/web_device.dart:210:49)
#10     GoogleChromeDevice.sdkNameAndVersion (package:flutter_tools/src/web/web_device.dart)
#11     RunCommand._sharedAnalyticsUsageValues.<anonymous closure> (package:flutter_tools/src/commands/run.dart:533:38)
<asynchronous suspension>
#12     RunCommand.unifiedAnalyticsUsageValues (package:flutter_tools/src/commands/run.dart:495:47)
<asynchronous suspension>
#13     Future.wait.<anonymous closure> (dart:async/future.dart:520:21)
<asynchronous suspension>
#14     FlutterCommand.verifyThenRunCommand (package:flutter_tools/src/runner/flutter_command.dart:1804:46)
<asynchronous suspension>
#15     FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1450:27)
<asynchronous suspension>
#16     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:153:19)
<asynchronous suspension>
#17     CommandRunner.runCommand (package:args/command_runner.dart:212:13)
<asynchronous suspension>
#18     FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:421:9)
<asynchronous suspension>
#19     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:153:19)
<asynchronous suspension>
#20     FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:364:5)
<asynchronous suspension>
#21     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:131:9)
<asynchronous suspension>
#22     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:153:19)
<asynchronous suspension>
#23     main (package:flutter_tools/executable.dart:94:3)
<asynchronous suspension>
```

## flutter doctor

```
[32m[✓][39m Flutter (Channel stable, 3.27.1, on Microsoft Windows [Version 10.0.26100.4061], locale en-IN)
    [32m•[39m Flutter version 3.27.1 on channel stable at C:\Users\<USER>\dev\flutter
    [32m•[39m Upstream repository https://github.com/flutter/flutter.git
    [32m•[39m Framework revision 17025dd882 (5 months ago), 2024-12-17 03:23:09 +0900
    [32m•[39m Engine revision cb4b5fff73
    [32m•[39m Dart version 3.6.0
    [32m•[39m DevTools version 2.40.2

[32m[✓][39m Windows Version (Installed version of Windows is version 10 or higher)

[33m[!][39m Android toolchain - develop for Android devices (Android SDK version 35.0.1)
    [32m•[39m Android SDK at C:\Users\<USER>\AppData\Local\Android\sdk
    [31m✗[39m cmdline-tools component is missing
      Run `path/to/sdkmanager --install "cmdline-tools;latest"`
      See https://developer.android.com/studio/command-line for more details.
    [31m✗[39m Android license status unknown.
      Run `flutter doctor --android-licenses` to accept the SDK licenses.
      See https://flutter.dev/to/windows-android-setup for more details.

[32m[✓][39m Chrome - develop for the web
    [32m•[39m Chrome at C:\Program Files\Google\Chrome\Application\chrome.exe

[33m[!][39m Visual Studio - develop Windows apps (Visual Studio Community 2022 17.13.1)
    [32m•[39m Visual Studio at C:\Program Files\Microsoft Visual Studio\2022\Community
    [32m•[39m Visual Studio Community 2022 version 17.13.35818.85
    [31m✗[39m Visual Studio is missing necessary components. Please re-run the Visual Studio installer for the "Desktop
      development with C++" workload, and include these components:
        MSVC v142 - VS 2019 C++ x64/x86 build tools
         - If there are multiple build tool versions available, install the latest
        C++ CMake tools for Windows
        Windows 10 SDK

[33m[!][39m Android Studio (version 2022.2)
    [32m•[39m Android Studio at C:\Program Files\Android\Android Studio
    [32m•[39m Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    [32m•[39m Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    [31m✗[39m Unable to find bundled Java version.
    [32m•[39m Try updating or re-installing Android Studio.

[32m[✓][39m Android Studio (version 2024.1)
    [32m•[39m Android Studio at C:\Program Files\Android\Android Studio1
    [32m•[39m Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    [32m•[39m Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    [32m•[39m Java version OpenJDK Runtime Environment (build 17.0.11+0--11852314)

[32m[✓][39m VS Code (version 1.100.2)
    [32m•[39m VS Code at C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code
    [32m•[39m Flutter extension version 3.110.0

[31m[☠][39m Connected device (the doctor check crashed)
    [31m✗[39m Due to an error, the doctor check did not complete. If the error message below is not helpful, please let us
      know about this issue at https://github.com/flutter/flutter/issues.
    [31m✗[39m Exception: Connected device exceeded maximum allowed duration of 0:04:30.000000
    [32m•[39m 

[32m[✓][39m Network resources
    [32m•[39m All expected network resources are available.

[33m![39m Doctor found issues in 4 categories.
```
