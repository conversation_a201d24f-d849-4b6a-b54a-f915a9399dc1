<?php
header('Content-Type: text/html; charset=utf-8');

// User Data Viewer - Now with Real Database Credentials
echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>🔍 MagicMate User Data Viewer</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "table { border-collapse: collapse; width: 100%; margin: 20px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }";
echo "th { background-color: #f2f2f2; }";
echo ".success { color: green; }";
echo ".error { color: red; }";
echo ".warning { color: orange; }";
echo ".info { color: blue; }";
echo ".card { background: #f9f9f9; padding: 15px; margin: 10px 0; border-radius: 5px; }";
echo "</style>";
echo "</head><body>";

echo "<h1>🔍 MagicMate User Data Viewer</h1>";
echo "<p><strong>Database:</strong> u158044629_linkinblink</p>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<hr>";

try {
    // Connect with real credentials
    $evmulti = new mysqli("localhost", "u158044629_linkinblink", "K:l*&&yO7", "u158044629_linkinblink");
    $evmulti->set_charset("utf8mb4");
    
    if ($evmulti->connect_error) {
        throw new Exception("Connection failed: " . $evmulti->connect_error);
    }
    
    echo "<div class='card'>";
    echo "<h2 class='success'>✅ Database Connection Successful!</h2>";
    echo "<p><strong>Server Info:</strong> " . $evmulti->server_info . "</p>";
    echo "<p><strong>Host Info:</strong> " . $evmulti->host_info . "</p>";
    echo "</div>";
    
    // Check if tbl_user exists
    $table_check = $evmulti->query("SHOW TABLES LIKE 'tbl_user'");
    if ($table_check->num_rows == 0) {
        echo "<div class='card error'>";
        echo "<h2>❌ Table 'tbl_user' does not exist!</h2>";
        echo "<p>Available tables:</p>";
        $tables = $evmulti->query("SHOW TABLES");
        echo "<ul>";
        while ($table = $tables->fetch_array()) {
            echo "<li>" . $table[0] . "</li>";
        }
        echo "</ul>";
        echo "</div>";
        exit();
    }
    
    echo "<div class='card'>";
    echo "<h2 class='success'>✅ Table 'tbl_user' exists</h2>";
    echo "</div>";
    
    // Get user count
    $count_result = $evmulti->query("SELECT COUNT(*) as total FROM tbl_user");
    $total_users = $count_result->fetch_assoc()['total'];
    
    echo "<div class='card'>";
    echo "<h2 class='info'>👥 Total Users: " . $total_users . "</h2>";
    echo "</div>";
    
    if ($total_users > 0) {
        // Show user statistics
        echo "<div class='card'>";
        echo "<h3>📊 User Statistics</h3>";
        
        $active_users = $evmulti->query("SELECT COUNT(*) as count FROM tbl_user WHERE status = 1")->fetch_assoc()['count'];
        $inactive_users = $evmulti->query("SELECT COUNT(*) as count FROM tbl_user WHERE status = 0")->fetch_assoc()['count'];
        
        echo "<p><strong>Active Users:</strong> " . $active_users . "</p>";
        echo "<p><strong>Inactive Users:</strong> " . $inactive_users . "</p>";
        
        // Registration trends
        $today = $evmulti->query("SELECT COUNT(*) as count FROM tbl_user WHERE DATE(reg_date) = CURDATE()")->fetch_assoc()['count'];
        $this_week = $evmulti->query("SELECT COUNT(*) as count FROM tbl_user WHERE reg_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)")->fetch_assoc()['count'];
        $this_month = $evmulti->query("SELECT COUNT(*) as count FROM tbl_user WHERE reg_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)")->fetch_assoc()['count'];
        
        echo "<p><strong>Registered Today:</strong> " . $today . "</p>";
        echo "<p><strong>Registered This Week:</strong> " . $this_week . "</p>";
        echo "<p><strong>Registered This Month:</strong> " . $this_month . "</p>";
        echo "</div>";
        
        // Show recent users
        echo "<div class='card'>";
        echo "<h3>📱 Recent User Registrations (Last 20)</h3>";
        
        $recent_users = $evmulti->query("
            SELECT id, name, email, mobile, ccode, reg_date, status, wallet, refercode 
            FROM tbl_user 
            ORDER BY id DESC 
            LIMIT 20
        ");
        
        if ($recent_users->num_rows > 0) {
            echo "<table>";
            echo "<tr>";
            echo "<th>ID</th>";
            echo "<th>Name</th>";
            echo "<th>Email</th>";
            echo "<th>Mobile</th>";
            echo "<th>Country Code</th>";
            echo "<th>Registration Date</th>";
            echo "<th>Status</th>";
            echo "<th>Wallet</th>";
            echo "<th>Refer Code</th>";
            echo "</tr>";
            
            while ($user = $recent_users->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $user['id'] . "</td>";
                echo "<td>" . htmlspecialchars($user['name']) . "</td>";
                echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                echo "<td>" . htmlspecialchars($user['mobile']) . "</td>";
                echo "<td>" . htmlspecialchars($user['ccode']) . "</td>";
                echo "<td>" . $user['reg_date'] . "</td>";
                echo "<td>" . ($user['status'] == 1 ? '<span class="success">✅ Active</span>' : '<span class="error">❌ Inactive</span>') . "</td>";
                echo "<td>" . $user['wallet'] . "</td>";
                echo "<td>" . $user['refercode'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        echo "</div>";
        
        // Check for duplicates
        echo "<div class='card'>";
        echo "<h3>🔍 Duplicate Analysis</h3>";
        
        $duplicate_emails = $evmulti->query("
            SELECT email, COUNT(*) as count 
            FROM tbl_user 
            GROUP BY email 
            HAVING count > 1
        ");
        
        $duplicate_mobiles = $evmulti->query("
            SELECT mobile, COUNT(*) as count 
            FROM tbl_user 
            GROUP BY mobile 
            HAVING count > 1
        ");
        
        echo "<p><strong>Duplicate Emails:</strong> " . $duplicate_emails->num_rows . "</p>";
        echo "<p><strong>Duplicate Mobiles:</strong> " . $duplicate_mobiles->num_rows . "</p>";
        
        if ($duplicate_emails->num_rows > 0) {
            echo "<h4>📧 Duplicate Email Addresses:</h4>";
            echo "<table>";
            echo "<tr><th>Email</th><th>Count</th></tr>";
            while ($dup = $duplicate_emails->fetch_assoc()) {
                echo "<tr><td>" . htmlspecialchars($dup['email']) . "</td><td>" . $dup['count'] . "</td></tr>";
            }
            echo "</table>";
        }
        
        if ($duplicate_mobiles->num_rows > 0) {
            echo "<h4>📱 Duplicate Mobile Numbers:</h4>";
            echo "<table>";
            echo "<tr><th>Mobile</th><th>Count</th></tr>";
            while ($dup = $duplicate_mobiles->fetch_assoc()) {
                echo "<tr><td>" . htmlspecialchars($dup['mobile']) . "</td><td>" . $dup['count'] . "</td></tr>";
            }
            echo "</table>";
        }
        echo "</div>";
        
        // Show wallet statistics
        echo "<div class='card'>";
        echo "<h3>💰 Wallet Statistics</h3>";
        
        $wallet_stats = $evmulti->query("
            SELECT 
                SUM(wallet) as total_wallet,
                AVG(wallet) as avg_wallet,
                MAX(wallet) as max_wallet,
                MIN(wallet) as min_wallet
            FROM tbl_user
        ")->fetch_assoc();
        
        echo "<p><strong>Total Wallet Amount:</strong> " . $wallet_stats['total_wallet'] . "</p>";
        echo "<p><strong>Average Wallet:</strong> " . round($wallet_stats['avg_wallet'], 2) . "</p>";
        echo "<p><strong>Highest Wallet:</strong> " . $wallet_stats['max_wallet'] . "</p>";
        echo "<p><strong>Lowest Wallet:</strong> " . $wallet_stats['min_wallet'] . "</p>";
        echo "</div>";
        
    } else {
        echo "<div class='card warning'>";
        echo "<h2>⚠️ No Users Found in Database</h2>";
        echo "<p>The tbl_user table exists but contains no data.</p>";
        echo "<p>This could mean:</p>";
        echo "<ul>";
        echo "<li>No users have registered yet</li>";
        echo "<li>Registration API is not working</li>";
        echo "<li>Data was deleted or moved</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    // Check other related tables
    echo "<div class='card'>";
    echo "<h3>🗄️ Related Tables Check</h3>";
    
    $tables_to_check = ['tbl_sponsore', 'tbl_booking', 'wallet_report', 'tbl_event', 'admin'];
    
    foreach ($tables_to_check as $table) {
        $check = $evmulti->query("SHOW TABLES LIKE '$table'");
        if ($check->num_rows > 0) {
            $count = $evmulti->query("SELECT COUNT(*) as count FROM $table")->fetch_assoc()['count'];
            echo "<p class='success'>✅ $table: $count records</p>";
        } else {
            echo "<p class='error'>❌ $table: Table not found</p>";
        }
    }
    echo "</div>";
    
    // Admin panel access info
    echo "<div class='card'>";
    echo "<h3>🖥️ Admin Panel Access</h3>";
    echo "<p><strong>Admin Panel URL:</strong> <a href='index.php' target='_blank'>index.php</a></p>";
    echo "<p><strong>User List URL:</strong> <a href='list_user.php' target='_blank'>list_user.php</a></p>";
    echo "<p><strong>Default Admin Credentials:</strong></p>";
    echo "<ul>";
    echo "<li>Username: admin</li>";
    echo "<li>Password: admin@123</li>";
    echo "</ul>";
    echo "</div>";
    
    $evmulti->close();
    
} catch (Exception $e) {
    echo "<div class='card error'>";
    echo "<h2>❌ Database Error</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>Report generated at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</body></html>";
?>
