import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:magicmate/utils/Colors.dart';
import 'package:magicmate/controller/bulksmsind_otp_controller.dart';

class BulkSmsIndTestScreen extends StatefulWidget {
  const BulkSmsIndTestScreen({super.key});

  @override
  State<BulkSmsIndTestScreen> createState() => _BulkSmsIndTestScreenState();
}

class _BulkSmsIndTestScreenState extends State<BulkSmsIndTestScreen> {
  final _mobileController = TextEditingController();
  final BulkSmsIndOtpController _controller =
      Get.find<BulkSmsIndOtpController>();

  String _testResult = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _mobileController.text = '+919876543210'; // Default test number
  }

  @override
  void dispose() {
    _mobileController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: WhiteColor,
      appBar: AppBar(
        backgroundColor: WhiteColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: BlackColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'BulkSMSInd Test',
          style: TextStyle(
            color: BlackColor,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test BulkSMSInd API Integration',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: BlackColor,
              ),
            ),

            SizedBox(height: 20),

            // Mobile Number Input
            TextField(
              controller: _mobileController,
              decoration: InputDecoration(
                labelText: 'Mobile Number',
                hintText: '+919876543210',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
            ),

            SizedBox(height: 20),

            // Test Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testSimpleAPI,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Test Simple API',
                      style: TextStyle(color: WhiteColor),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testOTPAPI,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Test OTP API',
                      style: TextStyle(color: WhiteColor),
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: 12),

            // Balance Check Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _checkBalance,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  padding: EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Check Balance',
                  style: TextStyle(color: WhiteColor),
                ),
              ),
            ),

            SizedBox(height: 20),

            // Loading Indicator
            if (_isLoading)
              Center(
                child: CircularProgressIndicator(),
              ),

            // Results
            if (_testResult.isNotEmpty) ...[
              Text(
                'Test Results:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: BlackColor,
                ),
              ),
              SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Text(
                  _testResult,
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ],

            SizedBox(height: 20),

            // Instructions
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Instructions:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        '1. Enter your mobile number\n'
                        '2. Click "Test Simple API" to test basic connectivity\n'
                        '3. Click "Test OTP API" to test OTP sending\n'
                        '4. Click "Check Balance" to verify account balance\n'
                        '5. Check the console for detailed debug logs\n'
                        '6. Verify SMS delivery on the mobile device',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testSimpleAPI() async {
    if (_mobileController.text.isEmpty) {
      _showError('Please enter a mobile number');
      return;
    }

    setState(() {
      _isLoading = true;
      _testResult = '';
    });

    try {
      final result = await _controller.testBulkSMSIndAPI(
        mobile: _mobileController.text,
      );

      setState(() {
        _testResult = 'Simple API Test Result:\n${result.toString()}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _testResult = 'Simple API Test Error:\n$e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testOTPAPI() async {
    if (_mobileController.text.isEmpty) {
      _showError('Please enter a mobile number');
      return;
    }

    setState(() {
      _isLoading = true;
      _testResult = '';
    });

    try {
      // Try proxy method first (CORS-free)
      final result = await _controller.bulkSmsIndOtpApiProxy(
        mobile: _mobileController.text,
      );

      setState(() {
        _testResult = 'OTP API Test Result (Proxy):\n${result.toString()}';
        _isLoading = false;
      });

      if (result['Result'] == 'true') {
        _showSuccess(
            'OTP sent successfully via server proxy! Check your mobile device.');
      } else {
        _showError('OTP sending failed: ${result['ResponseMsg']}');
      }
    } catch (e) {
      setState(() {
        _testResult = 'OTP API Test Error:\n$e';
        _isLoading = false;
      });
    }
  }

  Future<void> _checkBalance() async {
    setState(() {
      _isLoading = true;
      _testResult = '';
    });

    try {
      final result = await _controller.checkBalance();

      setState(() {
        _testResult = 'Balance Check Result:\n${result.toString()}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _testResult = 'Balance Check Error:\n$e';
        _isLoading = false;
      });
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }
}
