{"ast": null, "code": "'use client';\n\nimport _objectSpread from \"G:/linkinblink/hotel-dashboard/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"G:/linkinblink/hotel-dashboard/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"defaultProps\", \"mixins\", \"overrides\", \"palette\", \"props\", \"styleOverrides\"],\n  _excluded2 = [\"type\", \"mode\"];\nimport { createBreakpoints, createSpacing } from '@mui/system';\nexport default function adaptV4Theme(inputTheme) {\n  if (process.env.NODE_ENV !== 'production') {\n    console.warn(['MUI: adaptV4Theme() is deprecated.', 'Follow the upgrade guide on https://mui.com/r/migration-v4#theme.'].join('\\n'));\n  }\n  const {\n      defaultProps = {},\n      mixins = {},\n      overrides = {},\n      palette = {},\n      props = {},\n      styleOverrides = {}\n    } = inputTheme,\n    other = _objectWithoutProperties(inputTheme, _excluded);\n  const theme = _objectSpread(_objectSpread({}, other), {}, {\n    components: {}\n  });\n\n  // default props\n  Object.keys(defaultProps).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.defaultProps = defaultProps[component];\n    theme.components[component] = componentValue;\n  });\n  Object.keys(props).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.defaultProps = props[component];\n    theme.components[component] = componentValue;\n  });\n\n  // CSS overrides\n  Object.keys(styleOverrides).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.styleOverrides = styleOverrides[component];\n    theme.components[component] = componentValue;\n  });\n  Object.keys(overrides).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.styleOverrides = overrides[component];\n    theme.components[component] = componentValue;\n  });\n\n  // theme.spacing\n  theme.spacing = createSpacing(inputTheme.spacing);\n\n  // theme.mixins.gutters\n  const breakpoints = createBreakpoints(inputTheme.breakpoints || {});\n  const spacing = theme.spacing;\n  theme.mixins = _objectSpread({\n    gutters: function () {\n      let styles = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      return _objectSpread(_objectSpread({\n        paddingLeft: spacing(2),\n        paddingRight: spacing(2)\n      }, styles), {}, {\n        [breakpoints.up('sm')]: _objectSpread({\n          paddingLeft: spacing(3),\n          paddingRight: spacing(3)\n        }, styles[breakpoints.up('sm')])\n      });\n    }\n  }, mixins);\n  const {\n      type: typeInput,\n      mode: modeInput\n    } = palette,\n    paletteRest = _objectWithoutProperties(palette, _excluded2);\n  const finalMode = modeInput || typeInput || 'light';\n  theme.palette = _objectSpread({\n    // theme.palette.text.hint\n    text: {\n      hint: finalMode === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.38)'\n    },\n    mode: finalMode,\n    type: finalMode\n  }, paletteRest);\n  return theme;\n}", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "_excluded2", "createBreakpoints", "createSpacing", "adaptV4Theme", "inputTheme", "process", "env", "NODE_ENV", "console", "warn", "join", "defaultProps", "mixins", "overrides", "palette", "props", "styleOverrides", "other", "theme", "components", "Object", "keys", "for<PERSON>ach", "component", "componentValue", "spacing", "breakpoints", "gutters", "styles", "arguments", "length", "undefined", "paddingLeft", "paddingRight", "up", "type", "typeInput", "mode", "modeInput", "paletteRest", "finalMode", "text", "hint"], "sources": ["G:/linkinblink/hotel-dashboard/node_modules/@mui/material/esm/styles/adaptV4Theme.js"], "sourcesContent": ["'use client';\n\nimport { createBreakpoints, createSpacing } from '@mui/system';\nexport default function adaptV4Theme(inputTheme) {\n  if (process.env.NODE_ENV !== 'production') {\n    console.warn(['MUI: adaptV4Theme() is deprecated.', 'Follow the upgrade guide on https://mui.com/r/migration-v4#theme.'].join('\\n'));\n  }\n  const {\n    defaultProps = {},\n    mixins = {},\n    overrides = {},\n    palette = {},\n    props = {},\n    styleOverrides = {},\n    ...other\n  } = inputTheme;\n  const theme = {\n    ...other,\n    components: {}\n  };\n\n  // default props\n  Object.keys(defaultProps).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.defaultProps = defaultProps[component];\n    theme.components[component] = componentValue;\n  });\n  Object.keys(props).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.defaultProps = props[component];\n    theme.components[component] = componentValue;\n  });\n\n  // CSS overrides\n  Object.keys(styleOverrides).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.styleOverrides = styleOverrides[component];\n    theme.components[component] = componentValue;\n  });\n  Object.keys(overrides).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.styleOverrides = overrides[component];\n    theme.components[component] = componentValue;\n  });\n\n  // theme.spacing\n  theme.spacing = createSpacing(inputTheme.spacing);\n\n  // theme.mixins.gutters\n  const breakpoints = createBreakpoints(inputTheme.breakpoints || {});\n  const spacing = theme.spacing;\n  theme.mixins = {\n    gutters: (styles = {}) => {\n      return {\n        paddingLeft: spacing(2),\n        paddingRight: spacing(2),\n        ...styles,\n        [breakpoints.up('sm')]: {\n          paddingLeft: spacing(3),\n          paddingRight: spacing(3),\n          ...styles[breakpoints.up('sm')]\n        }\n      };\n    },\n    ...mixins\n  };\n  const {\n    type: typeInput,\n    mode: modeInput,\n    ...paletteRest\n  } = palette;\n  const finalMode = modeInput || typeInput || 'light';\n  theme.palette = {\n    // theme.palette.text.hint\n    text: {\n      hint: finalMode === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.38)'\n    },\n    mode: finalMode,\n    type: finalMode,\n    ...paletteRest\n  };\n  return theme;\n}"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;EAAAC,UAAA;AAEb,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,aAAa;AAC9D,eAAe,SAASC,YAAYA,CAACC,UAAU,EAAE;EAC/C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCC,OAAO,CAACC,IAAI,CAAC,CAAC,oCAAoC,EAAE,mEAAmE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EACtI;EACA,MAAM;MACJC,YAAY,GAAG,CAAC,CAAC;MACjBC,MAAM,GAAG,CAAC,CAAC;MACXC,SAAS,GAAG,CAAC,CAAC;MACdC,OAAO,GAAG,CAAC,CAAC;MACZC,KAAK,GAAG,CAAC,CAAC;MACVC,cAAc,GAAG,CAAC;IAEpB,CAAC,GAAGZ,UAAU;IADTa,KAAK,GAAAnB,wBAAA,CACNM,UAAU,EAAAL,SAAA;EACd,MAAMmB,KAAK,GAAArB,aAAA,CAAAA,aAAA,KACNoB,KAAK;IACRE,UAAU,EAAE,CAAC;EAAC,EACf;;EAED;EACAC,MAAM,CAACC,IAAI,CAACV,YAAY,CAAC,CAACW,OAAO,CAACC,SAAS,IAAI;IAC7C,MAAMC,cAAc,GAAGN,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC;IACxDC,cAAc,CAACb,YAAY,GAAGA,YAAY,CAACY,SAAS,CAAC;IACrDL,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,GAAGC,cAAc;EAC9C,CAAC,CAAC;EACFJ,MAAM,CAACC,IAAI,CAACN,KAAK,CAAC,CAACO,OAAO,CAACC,SAAS,IAAI;IACtC,MAAMC,cAAc,GAAGN,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC;IACxDC,cAAc,CAACb,YAAY,GAAGI,KAAK,CAACQ,SAAS,CAAC;IAC9CL,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,GAAGC,cAAc;EAC9C,CAAC,CAAC;;EAEF;EACAJ,MAAM,CAACC,IAAI,CAACL,cAAc,CAAC,CAACM,OAAO,CAACC,SAAS,IAAI;IAC/C,MAAMC,cAAc,GAAGN,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC;IACxDC,cAAc,CAACR,cAAc,GAAGA,cAAc,CAACO,SAAS,CAAC;IACzDL,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,GAAGC,cAAc;EAC9C,CAAC,CAAC;EACFJ,MAAM,CAACC,IAAI,CAACR,SAAS,CAAC,CAACS,OAAO,CAACC,SAAS,IAAI;IAC1C,MAAMC,cAAc,GAAGN,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC;IACxDC,cAAc,CAACR,cAAc,GAAGH,SAAS,CAACU,SAAS,CAAC;IACpDL,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,GAAGC,cAAc;EAC9C,CAAC,CAAC;;EAEF;EACAN,KAAK,CAACO,OAAO,GAAGvB,aAAa,CAACE,UAAU,CAACqB,OAAO,CAAC;;EAEjD;EACA,MAAMC,WAAW,GAAGzB,iBAAiB,CAACG,UAAU,CAACsB,WAAW,IAAI,CAAC,CAAC,CAAC;EACnE,MAAMD,OAAO,GAAGP,KAAK,CAACO,OAAO;EAC7BP,KAAK,CAACN,MAAM,GAAAf,aAAA;IACV8B,OAAO,EAAE,SAAAA,CAAA,EAAiB;MAAA,IAAhBC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACnB,OAAAhC,aAAA,CAAAA,aAAA;QACEmC,WAAW,EAAEP,OAAO,CAAC,CAAC,CAAC;QACvBQ,YAAY,EAAER,OAAO,CAAC,CAAC;MAAC,GACrBG,MAAM;QACT,CAACF,WAAW,CAACQ,EAAE,CAAC,IAAI,CAAC,GAAArC,aAAA;UACnBmC,WAAW,EAAEP,OAAO,CAAC,CAAC,CAAC;UACvBQ,YAAY,EAAER,OAAO,CAAC,CAAC;QAAC,GACrBG,MAAM,CAACF,WAAW,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC;MAChC;IAEL;EAAC,GACEtB,MAAM,CACV;EACD,MAAM;MACJuB,IAAI,EAAEC,SAAS;MACfC,IAAI,EAAEC;IAER,CAAC,GAAGxB,OAAO;IADNyB,WAAW,GAAAzC,wBAAA,CACZgB,OAAO,EAAAd,UAAA;EACX,MAAMwC,SAAS,GAAGF,SAAS,IAAIF,SAAS,IAAI,OAAO;EACnDlB,KAAK,CAACJ,OAAO,GAAAjB,aAAA;IACX;IACA4C,IAAI,EAAE;MACJC,IAAI,EAAEF,SAAS,KAAK,MAAM,GAAG,0BAA0B,GAAG;IAC5D,CAAC;IACDH,IAAI,EAAEG,SAAS;IACfL,IAAI,EAAEK;EAAS,GACZD,WAAW,CACf;EACD,OAAOrB,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}