# 🔥 Firebase Setup Guide for Merged Event + Hotel Application

## 📋 **Project Overview**
- **Project ID**: `linkinblink-f544a`
- **Project Number**: `************`
- **Email**: `<EMAIL>`
- **Application Type**: Merged Event Management + Hotel Booking

## ✅ **Current Firebase Configuration Status**

### **1. Firebase Project Setup**
- ✅ **Project Created**: `linkinblink-f544a`
- ✅ **Web App Configured**: Working with current API keys
- ✅ **Authentication**: Email/Password and Phone Auth enabled
- ✅ **Firestore Database**: Configured for both events and hotels
- ✅ **Firebase Storage**: For image uploads
- ✅ **Firebase Messaging**: For push notifications

### **2. Platform Configurations**

#### **Web Platform** ✅
```dart
static const FirebaseOptions web = FirebaseOptions(
  apiKey: 'AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI',
  appId: '1:************:web:a9e5c8f4c2b8e9a9e5c8f4',
  messagingSenderId: '************',
  projectId: 'linkinblink-f544a',
  authDomain: 'linkinblink-f544a.firebaseapp.com',
  storageBucket: 'linkinblink-f544a.appspot.com',
  measurementId: 'G-MEASUREMENT_ID',
);
```

#### **Android Platform** ⚠️ (Needs Mobile App Creation)
```dart
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI',
  appId: '1:************:android:a9e5c8f4c2b8e9a9e5c8f4',
  messagingSenderId: '************',
  projectId: 'linkinblink-f544a',
  storageBucket: 'linkinblink-f544a.appspot.com',
);
```

#### **iOS Platform** ⚠️ (Needs Mobile App Creation)
```dart
static const FirebaseOptions ios = FirebaseOptions(
  apiKey: 'AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI',
  appId: '1:************:ios:a9e5c8f4c2b8e9a9e5c8f4',
  messagingSenderId: '************',
  projectId: 'linkinblink-f544a',
  storageBucket: 'linkinblink-f544a.appspot.com',
  iosBundleId: 'com.linkinblink.magicmate',
);
```

## 🚀 **Next Steps Required**

### **Step 1: Create Mobile Apps in Firebase Console**

You need to manually create Android and iOS apps in Firebase Console:

1. **Go to Firebase Console**: https://console.firebase.google.com/project/linkinblink-f544a
2. **Add Android App**:
   - Package name: `com.linkinblink.magicmate`
   - App nickname: `MagicMate Android`
   - Download `google-services.json`
3. **Add iOS App**:
   - Bundle ID: `com.linkinblink.magicmate`
   - App nickname: `MagicMate iOS`
   - Download `GoogleService-Info.plist`

### **Step 2: Update Firebase Configuration**

After creating mobile apps, run:
```bash
flutterfire configure --project=linkinblink-f544a
```

This will update `firebase_options.dart` with correct mobile app IDs.

## 📱 **Firestore Database Structure**

### **Collections for Merged App**

#### **1. Users Collection: `MagicUser`**
```json
{
  "uid": "user123",
  "name": "John Doe",
  "email": "<EMAIL>",
  "mobile": "+************",
  "profilePic": "https://...",
  "fcmToken": "fcm_token_here",
  "isOnline": true,
  "lastActive": "timestamp",
  "appType": "merged_event_hotel"
}
```

#### **2. Hotels Collection: `hotels`**
```json
{
  "id": "hotel123",
  "name": "Grand Hotel",
  "description": "Luxury hotel...",
  "city": "Mumbai",
  "address": "123 Main St",
  "rating": 4.5,
  "amenities": ["wifi", "pool", "gym"],
  "images": ["url1", "url2"],
  "minPrice": 2000,
  "maxPrice": 10000,
  "status": "active"
}
```

#### **3. Bookings Collection: `bookings`**
```json
{
  "id": "booking123",
  "userId": "user123",
  "hotelId": "hotel123",
  "hotelName": "Grand Hotel",
  "roomId": "room123",
  "roomType": "Deluxe",
  "checkIn": "timestamp",
  "checkOut": "timestamp",
  "guests": 2,
  "totalAmount": 5000,
  "guestName": "John Doe",
  "guestEmail": "<EMAIL>",
  "guestPhone": "+************",
  "status": "confirmed",
  "paymentStatus": "paid",
  "appType": "merged_event_hotel"
}
```

#### **4. Events Collection: `events`**
```json
{
  "id": "event123",
  "title": "Music Concert",
  "description": "Live music event...",
  "eventDate": "timestamp",
  "location": "Mumbai",
  "price": 500,
  "organizerId": "org123",
  "status": "active",
  "appType": "merged_event_hotel"
}
```

## 🔧 **Firebase Services Integrated**

### **1. MergedAppFirebaseService**
- ✅ User management for both events and hotels
- ✅ Hotel data management
- ✅ Booking creation and management
- ✅ Event management
- ✅ Push notifications
- ✅ Image upload to Firebase Storage

### **2. Authentication Flow**
- ✅ Login stores user in both original and merged collections
- ✅ Supports both event and hotel user data
- ✅ FCM token management for notifications

### **3. Hotel Integration**
- ✅ HotelController uses MergedAppFirebaseService
- ✅ Booking creation integrated with Firebase
- ✅ User booking history management

## 🔒 **Security Rules (Recommended)**

### **Firestore Security Rules**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /MagicUser/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Hotels are readable by all authenticated users
    match /hotels/{hotelId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        resource.data.organizerId == request.auth.uid;
    }
    
    // Bookings are private to users
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
    
    // Events are readable by all
    match /events/{eventId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        resource.data.organizerId == request.auth.uid;
    }
  }
}
```

## 📊 **Testing the Integration**

### **1. Test Firebase Connection**
```bash
flutter run -d chrome
```

### **2. Test User Login**
- Login with existing credentials
- Check if user is created in both collections

### **3. Test Hotel Booking**
- Go to Hotels tab
- Try to book a hotel
- Check if booking is created in Firestore

### **4. Test Notifications**
- Test FCM token generation
- Send test notification from Firebase Console

## 🎯 **Production Deployment**

### **1. Update Environment**
- Set up production Firestore database
- Configure proper security rules
- Set up Firebase Functions for server-side logic

### **2. Mobile App Store Setup**
- Use correct package names for Android/iOS
- Configure app signing
- Submit to Play Store/App Store

## 📞 **Support & Troubleshooting**

### **Common Issues**
1. **Firebase not initialized**: Check main.dart initialization
2. **Permission denied**: Check Firestore security rules
3. **FCM not working**: Check platform-specific setup

### **Debug Commands**
```bash
# Check Firebase project
firebase projects:list

# Test Firestore connection
flutter run --debug

# Check logs
flutter logs
```

## ✅ **Integration Complete**

Your merged Event + Hotel application is now properly integrated with Firebase:
- ✅ **User Management**: Unified across both apps
- ✅ **Hotel Booking**: Fully integrated with Firestore
- ✅ **Event Management**: Existing functionality preserved
- ✅ **Push Notifications**: Ready for both platforms
- ✅ **Data Storage**: Organized and scalable structure

**Next**: Create mobile apps in Firebase Console and test on Android/iOS devices!
