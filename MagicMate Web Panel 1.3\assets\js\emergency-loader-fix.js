/**
 * EMERGENCY LOADER FIX
 * This script forcefully removes the loader and ensures the page displays
 */

console.log("🚨 Emergency loader fix activated!");

// Method 1: Immediate removal
function removeLoaderImmediately() {
    const loaders = document.querySelectorAll('.loader-wrapper, .loader');
    loaders.forEach(loader => {
        loader.style.display = 'none';
        loader.style.opacity = '0';
        loader.style.visibility = 'hidden';
        loader.remove();
    });
    console.log("✅ Loader removed immediately");
}

// Method 2: jQuery removal (if available)
function removeLoaderWithJQuery() {
    if (typeof $ !== 'undefined') {
        $('.loader-wrapper').hide().remove();
        $('.loader').hide().remove();
        console.log("✅ Loader removed with jQuery");
    }
}

// Method 3: CSS override
function addLoaderHideCSS() {
    const style = document.createElement('style');
    style.textContent = `
        .loader-wrapper, .loader {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
            z-index: -9999 !important;
        }
        body {
            overflow: visible !important;
        }
    `;
    document.head.appendChild(style);
    console.log("✅ Loader hide CSS added");
}

// Method 4: Force body visibility
function forceBodyVisibility() {
    document.body.style.overflow = 'visible';
    document.body.style.visibility = 'visible';
    document.body.style.opacity = '1';
    console.log("✅ Body visibility forced");
}

// Execute all methods immediately
removeLoaderImmediately();
addLoaderHideCSS();
forceBodyVisibility();

// Execute when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        removeLoaderImmediately();
        removeLoaderWithJQuery();
        forceBodyVisibility();
        console.log("✅ Loader fix executed on DOM ready");
    });
} else {
    removeLoaderImmediately();
    removeLoaderWithJQuery();
    forceBodyVisibility();
    console.log("✅ Loader fix executed immediately (DOM already ready)");
}

// Execute when window loads
window.addEventListener('load', function() {
    removeLoaderImmediately();
    removeLoaderWithJQuery();
    forceBodyVisibility();
    console.log("✅ Loader fix executed on window load");
});

// Backup: Execute after 1 second
setTimeout(function() {
    removeLoaderImmediately();
    removeLoaderWithJQuery();
    forceBodyVisibility();
    console.log("✅ Loader fix executed after 1 second");
}, 1000);

// Emergency: Execute after 3 seconds
setTimeout(function() {
    removeLoaderImmediately();
    removeLoaderWithJQuery();
    forceBodyVisibility();
    console.log("🚨 Emergency loader fix executed after 3 seconds");
}, 3000);

// Nuclear option: Execute every 2 seconds for 10 seconds
let attempts = 0;
const maxAttempts = 5;
const interval = setInterval(function() {
    attempts++;
    removeLoaderImmediately();
    removeLoaderWithJQuery();
    forceBodyVisibility();
    console.log(`🔄 Loader fix attempt ${attempts}/${maxAttempts}`);
    
    if (attempts >= maxAttempts) {
        clearInterval(interval);
        console.log("✅ Loader fix attempts completed");
    }
}, 2000);

// Monitor for any new loaders being added
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        mutation.addedNodes.forEach(function(node) {
            if (node.nodeType === 1) { // Element node
                if (node.classList && (node.classList.contains('loader-wrapper') || node.classList.contains('loader'))) {
                    node.style.display = 'none';
                    node.remove();
                    console.log("🚫 Blocked new loader from appearing");
                }
            }
        });
    });
});

// Start observing
observer.observe(document.body, {
    childList: true,
    subtree: true
});

console.log("🛡️ Loader monitoring active - no loaders will survive!");

// Final message
setTimeout(function() {
    console.log("🎉 Emergency loader fix complete! Page should be visible now.");
}, 5000);
