Stack trace:
Frame         Function      Args
0007FFFF7B40  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF6A40) msys-2.0.dll+0x1FE8E
0007FFFF7B40  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF7E18) msys-2.0.dll+0x67F9
0007FFFF7B40  000210046832 (000210286019, 0007FFFF79F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF7B40  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF7B40  000210068E24 (0007FFFF7B50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF7E20  00021006A225 (0007FFFF7B50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDC1340000 ntdll.dll
7FFDC0780000 KERNEL32.DLL
7FFDBEAE0000 KERNELBASE.dll
7FFDC00D0000 USER32.dll
7FFDBEF70000 win32u.dll
7FFDBFA30000 GDI32.dll
7FFDBEFA0000 gdi32full.dll
7FFDBE550000 msvcp_win.dll
7FFDBE780000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDBF5E0000 advapi32.dll
7FFDBF520000 msvcrt.dll
7FFDC0850000 sechost.dll
7FFDC0900000 RPCRT4.dll
7FFDBDA70000 CRYPTBASE.DLL
7FFDBEED0000 bcryptPrimitives.dll
7FFDBF4E0000 IMM32.DLL
