Stack trace:
Frame         Function      Args
0007FFFF9DD0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8CD0) msys-2.0.dll+0x1FE8E
0007FFFF9DD0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA0A8) msys-2.0.dll+0x67F9
0007FFFF9DD0  000210046832 (000210286019, 0007FFFF9C88, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DD0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9DD0  000210068E24 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA0B0  00021006A225 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFF8140000 ntdll.dll
7FFFF6DA0000 KERNEL32.DLL
7FFFF5290000 KERNELBASE.dll
7FFFF7C20000 USER32.dll
7FFFF5730000 win32u.dll
7FFFF78C0000 GDI32.dll
7FFFF5AD0000 gdi32full.dll
7FFFF5680000 msvcp_win.dll
7FFFF5980000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFF63C0000 advapi32.dll
7FFFF6EB0000 msvcrt.dll
7FFFF7DF0000 sechost.dll
7FFFF6490000 RPCRT4.dll
7FFFF4870000 CRYPTBASE.DLL
7FFFF58E0000 bcryptPrimitives.dll
7FFFF6E70000 IMM32.DLL
