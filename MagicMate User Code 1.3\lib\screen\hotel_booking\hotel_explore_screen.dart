import 'package:flutter/material.dart';
import 'package:magicmate/utils/Colors.dart';
import 'package:magicmate/model/fontfamily_model.dart';
import 'package:get/get.dart';
import 'package:magicmate/model/hotel_model.dart';
import 'package:magicmate/controller/hotel_controller.dart';
import 'package:magicmate/screen/hotel_booking/hotel_details_screen.dart';

class HotelExploreScreen extends StatefulWidget {
  const HotelExploreScreen({super.key});

  @override
  State<HotelExploreScreen> createState() => _HotelExploreScreenState();
}

class _HotelExploreScreenState extends State<HotelExploreScreen> {
  final _searchController = TextEditingController();
  bool _isSearching = false;
  bool _isFiltering = false;

  // Filter state
  RangeValues _priceRange = const RangeValues(1000, 20000);
  double _minRating = 0;
  List<String> _selectedAmenities = [];
  String? _selectedPropertyType;

  @override
  void initState() {
    super.initState();
    _isSearching = false;
    _isFiltering = false;
    _searchController.clear();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: WhiteColor,
      appBar: AppBar(
        backgroundColor: WhiteColor,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: Text(
          'Explore Hotels',
          style: TextStyle(
            color: BlackColor,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(Icons.filter_list, color: BlackColor),
            onPressed: _showFilterBottomSheet,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search hotels, locations...',
                prefixIcon: Icon(Icons.search, color: Colors.grey),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(Icons.clear, color: Colors.grey),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _isSearching = false;
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: gradient.defoultColor),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _isSearching = value.isNotEmpty;
                });
              },
            ),
          ),

          // Content Area
          Expanded(
            child: _isSearching ? _buildSearchResults() : _buildHotelGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: 80,
            color: Colors.grey,
          ),
          SizedBox(height: 20),
          Text(
            'Search Results',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 10),
          Text(
            'Hotel search functionality coming soon!',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHotelGrid() {
    return GridView.builder(
      padding: EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: 6, // Sample hotels
      itemBuilder: (context, index) {
        return _buildHotelCard(index);
      },
    );
  }

  Widget _buildHotelCard(int index) {
    final sampleHotels = [
      {
        'name': 'Luxury Resort',
        'price': '5000',
        'rating': '4.8',
        'address': 'Marine Drive, Mumbai',
        'city': 'Mumbai',
        'description':
            'Experience luxury at its finest with stunning ocean views and world-class amenities.',
        'amenities': ['WiFi', 'Pool', 'Spa', 'Restaurant', 'Gym'],
      },
      {
        'name': 'City Hotel',
        'price': '3000',
        'rating': '4.5',
        'address': 'Connaught Place, New Delhi',
        'city': 'New Delhi',
        'description':
            'Modern comfort in the heart of the city with easy access to major attractions.',
        'amenities': ['WiFi', 'Restaurant', 'Bar', 'Room Service'],
      },
      {
        'name': 'Beach Resort',
        'price': '7000',
        'rating': '4.9',
        'address': 'Calangute Beach, Goa',
        'city': 'Goa',
        'description':
            'Beachfront paradise with pristine sands and crystal clear waters.',
        'amenities': ['WiFi', 'Pool', 'Beach Access', 'Restaurant', 'Spa'],
      },
      {
        'name': 'Mountain Lodge',
        'price': '4500',
        'rating': '4.6',
        'address': 'Mall Road, Shimla',
        'city': 'Shimla',
        'description':
            'Cozy mountain retreat with breathtaking views and fresh mountain air.',
        'amenities': ['WiFi', 'Restaurant', 'Fireplace', 'Mountain View'],
      },
      {
        'name': 'Business Hotel',
        'price': '2500',
        'rating': '4.3',
        'address': 'Electronic City, Bangalore',
        'city': 'Bangalore',
        'description':
            'Perfect for business travelers with modern facilities and conference rooms.',
        'amenities': ['WiFi', 'Business Center', 'Restaurant', 'Gym'],
      },
      {
        'name': 'Heritage Hotel',
        'price': '6000',
        'rating': '4.7',
        'address': 'City Palace Road, Jaipur',
        'city': 'Jaipur',
        'description':
            'Royal heritage hotel with traditional architecture and modern comforts.',
        'amenities': ['WiFi', 'Restaurant', 'Heritage Tours', 'Spa'],
      },
    ];

    final hotelData = sampleHotels[index];

    // Create a sample Hotel object
    final hotel = Hotel(
      id: 'hotel_$index',
      name: hotelData['name'] as String,
      description: hotelData['description'] as String,
      address: hotelData['address'] as String,
      city: hotelData['city'] as String,
      country: 'India',
      rating: double.parse(hotelData['rating'] as String),
      amenities: List<String>.from(hotelData['amenities'] as List),
      images: [], // Empty for now
      price: double.parse(hotelData['price'] as String),
    );

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => HotelDetailsScreen(hotel: hotel),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 3,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                  color: Colors.grey.shade300,
                ),
                child: Center(
                  child: Icon(
                    Icons.hotel,
                    size: 40,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: Padding(
                padding: EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      hotelData['name']!,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.star, color: Colors.amber, size: 16),
                        SizedBox(width: 4),
                        Text(
                          hotelData['rating']!,
                          style: TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                    Spacer(),
                    Text(
                      '₹${hotelData['price']!}/night',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: gradient.defoultColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filter Hotels',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20),
            Text('Price Range'),
            RangeSlider(
              values: _priceRange,
              min: 500,
              max: 25000,
              divisions: 50,
              labels: RangeLabels(
                '₹${_priceRange.start.round()}',
                '₹${_priceRange.end.round()}',
              ),
              onChanged: (values) {
                setState(() {
                  _priceRange = values;
                });
              },
            ),
            SizedBox(height: 20),
            Text('Minimum Rating'),
            Slider(
              value: _minRating,
              min: 0,
              max: 5,
              divisions: 10,
              label: _minRating.toString(),
              onChanged: (value) {
                setState(() {
                  _minRating = value;
                });
              },
            ),
            Spacer(),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      setState(() {
                        _priceRange = const RangeValues(1000, 20000);
                        _minRating = 0;
                        _selectedAmenities.clear();
                        _selectedPropertyType = null;
                      });
                      Navigator.pop(context);
                    },
                    child: Text('Clear'),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      // Apply filters
                    },
                    child: Text('Apply'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
