<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Login Test</title>
    <link rel="stylesheet" type="text/css" href="assets/css/vendors/bootstrap.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Arial, sans-serif;
        }
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        .form-control {
            margin-bottom: 15px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        .btn:hover {
            opacity: 0.9;
        }
        h2 {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .test-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="test-info">
            <strong>🧪 Simple Login Test</strong><br>
            This page has NO LOADER and should display immediately.<br>
            If you see this, the loader issue is fixed!
        </div>
        
        <h2>Admin Login</h2>
        
        <form method="POST" action="">
            <input type="text" name="username" class="form-control" placeholder="Username" required>
            <input type="password" name="password" class="form-control" placeholder="Password" required>
            <select name="stype" class="form-control" required>
                <option value="">Select Type</option>
                <option value="mowner">Master Admin</option>
                <option value="sowner">Organizer Panel</option>
            </select>
            <button type="submit" name="login" class="btn">Login</button>
        </form>
        
        <?php
        session_start();
        
        if (isset($_POST['login'])) {
            // Simple test login - replace with your actual credentials
            $username = $_POST['username'];
            $password = $_POST['password'];
            $type = $_POST['stype'];
            
            // Test credentials (replace with your actual admin credentials)
            if ($username == 'admin' && $password == 'admin') {
                $_SESSION["evename"] = $username;
                $_SESSION["stype"] = $type;
                echo "<script>alert('Login successful! Redirecting to dashboard...'); window.location.href='dashboard.php';</script>";
            } else {
                echo "<div style='color: red; text-align: center; margin-top: 15px;'>Invalid credentials! Try: admin/admin</div>";
            }
        }
        ?>
        
        <div style="text-align: center; margin-top: 20px;">
            <a href="index.php" style="color: #667eea;">← Back to Main Login</a><br>
            <a href="quick_fix_test.php" style="color: #667eea;">🔧 Run System Test</a>
        </div>
    </div>
    
    <script src="assets/js/jquery-3.5.1.min.js"></script>
    <script>
        console.log("✅ Simple login page loaded successfully!");
        console.log("✅ No loader interference!");
        
        // Test if main login page works
        function testMainLogin() {
            window.location.href = 'index.php';
        }
        
        // Add test button
        setTimeout(function() {
            if (confirm("Simple login works! Test main login page now?")) {
                testMainLogin();
            }
        }, 3000);
    </script>
</body>
</html>
