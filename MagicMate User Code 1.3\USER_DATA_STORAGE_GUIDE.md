# 📱 MagicMate User App - Data Storage Guide

## 🗄️ **WHERE USER DATA IS STORED**

### **1. 🔧 Local Storage (Mobile Device)**

#### **GetStorage (Primary Local Storage):**
- **Library**: `get_storage` package
- **File**: `lib/Api/data_store.dart`
- **Function**: `save(Key, val)` and `getData.read(Key)`

#### **Key Data Stored Locally:**
```dart
// User login data
save("UserLogin", result["UserLogin"]);

// First time user flag
save("Firstuser", true);

// Remember login state
save("Remember", true);

// Currency setting
save("currency", result["currency"]);
```

#### **SharedPreferences (Secondary Storage):**
- **Used for**: App preferences, settings, counters
- **Examples**: Dark mode, language settings, notification preferences

### **2. 🌐 Server Storage (Database)**

#### **Server URL**: `https://magicmate.cscodetech.cloud/user_api/`

#### **Database Tables (Server-side):**
- **`tbl_user`**: Main user data (registration, profile, wallet)
- **`tbl_booking`**: Event bookings and tickets
- **`tbl_wallet`**: Wallet transactions
- **`tbl_favorite`**: User's favorite events
- **`tbl_notification`**: User notifications

### **3. 🔥 Firebase Storage**

#### **Firebase Collections:**
- **`Magic_Organization_rooms`**: Chat/messaging data
- **`users`**: Firebase user profiles
- **`notifications`**: Push notifications

#### **Firebase Features Used:**
- **Authentication**: User login/signup
- **Firestore**: Real-time chat data
- **Cloud Messaging**: Push notifications
- **Analytics**: User behavior tracking

## 📊 **DETAILED DATA BREAKDOWN**

### **🔐 User Login Data (Local Storage)**

When user logs in, this data is saved locally:
```json
{
  "UserLogin": {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "mobile": "phone_number",
    "pro_pic": "profile_image_url",
    "wallet": "wallet_balance",
    "refercode": "referral_code"
  }
}
```

### **🎫 Event Booking Data (Server)**

When user books an event:
- **Stored in**: Server database (`tbl_booking`)
- **API**: `book_ticket.php`
- **Data**: Event details, ticket info, payment status

### **💰 Wallet Data (Server + Local)**

- **Server**: All transactions in `tbl_wallet`
- **Local**: Current balance cached in `UserLogin`
- **API**: `u_wallet_report.php`, `u_wallet_up.php`

### **❤️ Favorites Data (Server)**

- **Stored in**: `tbl_favorite` table
- **API**: `u_fav.php`, `u_favlist.php`
- **Data**: User's liked events

### **🔔 Notifications (Firebase + Server)**

- **Firebase**: Real-time push notifications
- **Server**: Notification history in `tbl_notification`
- **API**: `notification.php`

## 🔍 **HOW TO ACCESS USER DATA**

### **1. 📱 From Mobile App (Local)**

```dart
// Get user login data
var userData = getData.read("UserLogin");
String userId = userData["id"];
String userName = userData["name"];
String userEmail = userData["email"];

// Check if user is logged in
bool isLoggedIn = getData.read("Remember") ?? false;

// Get currency setting
String currency = getData.read("currency");
```

### **2. 🌐 From Server (Database)**

```sql
-- Get user data
SELECT * FROM tbl_user WHERE id = 'user_id';

-- Get user bookings
SELECT * FROM tbl_booking WHERE uid = 'user_id';

-- Get wallet transactions
SELECT * FROM tbl_wallet WHERE uid = 'user_id';

-- Get favorite events
SELECT * FROM tbl_favorite WHERE uid = 'user_id';
```

### **3. 🔥 From Firebase**

```dart
// Get Firebase user data
FirebaseFirestore.instance
  .collection('Magic_Organization_rooms')
  .doc(userId)
  .get();

// Get chat messages
FirebaseFirestore.instance
  .collection('messages')
  .where('senderId', isEqualTo: userId)
  .get();
```

## 📂 **DATA FLOW DIAGRAM**

```
Mobile App (Local Storage)
    ↕️
Server Database (MySQL)
    ↕️
Firebase (Real-time data)
```

### **Registration Flow:**
1. **User registers** → Data sent to server
2. **Server saves** → `tbl_user` table
3. **Response sent** → App saves locally
4. **Firebase account** → Created for chat/notifications

### **Login Flow:**
1. **User logs in** → Credentials sent to server
2. **Server validates** → Returns user data
3. **App saves locally** → `UserLogin` object
4. **Firebase login** → For real-time features

### **Booking Flow:**
1. **User books event** → Data sent to server
2. **Server processes** → Saves in `tbl_booking`
3. **Payment processed** → Updates wallet
4. **Confirmation sent** → App updates UI

## 🔧 **ADMIN PANEL ACCESS**

### **View User Data:**
- **Login**: `https://your-domain.com/admin`
- **Users**: Admin panel → User List
- **Bookings**: Admin panel → Booking Management
- **Wallet**: Admin panel → Wallet Reports

### **Database Direct Access:**
- **Table**: `tbl_user`
- **Columns**: id, name, email, mobile, wallet, etc.
- **Query**: `SELECT * FROM tbl_user ORDER BY id DESC;`

## 📱 **MOBILE APP DATA LOCATIONS**

### **Android:**
- **GetStorage**: `/data/data/com.yourapp.package/app_flutter/`
- **SharedPreferences**: `/data/data/com.yourapp.package/shared_prefs/`

### **iOS:**
- **GetStorage**: `~/Library/Application Support/`
- **SharedPreferences**: `~/Library/Preferences/`

## 🔍 **DEBUGGING USER DATA**

### **Check Local Storage:**
```dart
// Print all stored data
print("UserLogin: ${getData.read("UserLogin")}");
print("Firstuser: ${getData.read("Firstuser")}");
print("Remember: ${getData.read("Remember")}");
print("Currency: ${getData.read("currency")}");
```

### **Check Server Data:**
- **API**: `https://your-domain.com/user_api/u_profile_edit.php`
- **Method**: POST with user ID
- **Response**: Complete user profile data

### **Check Firebase Data:**
- **Console**: Firebase Console → Firestore Database
- **Collection**: `Magic_Organization_rooms`
- **Document**: User ID

## 🎯 **SUMMARY**

**User data in MagicMate app is stored in THREE places:**

1. **📱 Local (Mobile Device)**:
   - Login credentials
   - App preferences
   - Cached data

2. **🌐 Server (Database)**:
   - Complete user profile
   - Event bookings
   - Wallet transactions
   - Favorites

3. **🔥 Firebase**:
   - Chat messages
   - Real-time notifications
   - User presence

**To access user data, check the local storage first, then query the server database for complete information.**
