import 'dart:convert';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;

import '../model/bulksmsind_otp_model.dart';

class BulkSmsIndOtpController extends GetxController implements GetxService {
  BulkSmsIndOtpModel? bulkSmsIndOtpModel;
  BulkSmsIndBalanceModel? bulkSmsIndBalanceModel;
  BulkSmsIndDlrModel? bulkSmsIndDlrModel;

  // BulkSMSInd API Configuration
  static const String baseUrl = "http://sms.bulksmsind.in";
  static const String username = "hmescan";
  static const String apiKey = "e0117592-f761-4393-9772-31d4c0eb41cf";
  static const String sendername = "BULKSM"; // Default sender name
  static const String smstype = "TRANS"; // TRANS for transactional
  static const String peid = "1701159876885885613"; // Your PE ID from DLT
  static const String templateid =
      "1707172090686482394"; // Your template ID from DLT

  /// Send OTP SMS via BulkSMSInd API
  Future bulkSmsIndOtpApi({required String mobile}) async {
    try {
      // Generate 6-digit OTP
      String otp = _generateOTP();

      // Clean mobile number (remove + and any spaces)
      String cleanMobile =
          mobile.replaceAll('+', '').replaceAll(' ', '').replaceAll('-', '');

      // Ensure mobile number is in correct format (should start with country code)
      if (cleanMobile.startsWith('91') && cleanMobile.length == 12) {
        // Indian number with country code - good
      } else if (cleanMobile.length == 10) {
        // Indian number without country code - add it
        cleanMobile = '91$cleanMobile';
      }

      // Create OTP message - simplified for testing
      String message = "Your OTP is $otp. Valid for 10 minutes.";

      // Prepare API URL - Try without sendername first
      String apiUrl = "$baseUrl/v2/sendSMS"
          "?username=$username"
          "&message=${Uri.encodeComponent(message)}"
          "&smstype=$smstype"
          "&numbers=$cleanMobile"
          "&apikey=$apiKey";

      // Add DLT parameters if needed (comment out for testing)
      // "&peid=$peid"
      // "&templateid=$templateid";

      debugPrint("🚀 BulkSMSInd API URL: $apiUrl");
      debugPrint("📱 Sending OTP: $otp to mobile: $mobile");

      // Make API call with timeout
      var response = await http.get(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(Duration(seconds: 30));

      debugPrint("📥 BulkSMSInd Response: ${response.body}");
      debugPrint("📊 Status Code: ${response.statusCode}");

      if (response.statusCode == 200) {
        // Parse response
        var responseData = response.body;

        // BulkSMSInd returns plain text response, parse it
        Map<String, dynamic> parsedResponse =
            _parseResponse(responseData, otp, mobile);

        if (parsedResponse["Result"] == "true") {
          bulkSmsIndOtpModel = BulkSmsIndOtpModel.fromJson(parsedResponse);
          update();
          return parsedResponse;
        } else {
          print("BulkSMSInd Error: ${parsedResponse["ResponseMsg"]}");
          return {
            "Result": "false",
            "ResponseMsg": parsedResponse["ResponseMsg"] ?? "Failed to send OTP"
          };
        }
      } else {
        print("HTTP Error: ${response.statusCode}");
        return {"Result": "false", "ResponseMsg": "Network error occurred"};
      }
    } catch (e) {
      print("BulkSMSInd Exception: $e");
      return {"Result": "false", "ResponseMsg": "Failed to send OTP: $e"};
    }
  }

  /// Check SMS Balance
  Future checkBalance() async {
    try {
      String apiUrl = "$baseUrl/getSMSCredit"
          "?username=$username"
          "&apikey=$apiKey";

      var response = await http.get(Uri.parse(apiUrl));

      print("Balance Response: ${response.body}");

      if (response.statusCode == 200) {
        var responseData = response.body;
        Map<String, dynamic> parsedResponse =
            _parseBalanceResponse(responseData);

        if (parsedResponse["Result"] == "true") {
          bulkSmsIndBalanceModel =
              BulkSmsIndBalanceModel.fromJson(parsedResponse);
          update();
          return parsedResponse;
        }
      }

      return {"Result": "false", "ResponseMsg": "Failed to check balance"};
    } catch (e) {
      print("Balance Check Exception: $e");
      return {"Result": "false", "ResponseMsg": "Failed to check balance: $e"};
    }
  }

  /// Get Delivery Report
  Future getDeliveryReport({required String msgId}) async {
    try {
      String apiUrl = "$baseUrl/getDLR"
          "?username=$username"
          "&msgid=$msgId"
          "&apikey=$apiKey";

      var response = await http.get(Uri.parse(apiUrl));

      print("DLR Response: ${response.body}");

      if (response.statusCode == 200) {
        var responseData = response.body;
        Map<String, dynamic> parsedResponse =
            _parseDlrResponse(responseData, msgId);

        if (parsedResponse["Result"] == "true") {
          bulkSmsIndDlrModel = BulkSmsIndDlrModel.fromJson(parsedResponse);
          update();
          return parsedResponse;
        }
      }

      return {
        "Result": "false",
        "ResponseMsg": "Failed to get delivery report"
      };
    } catch (e) {
      print("DLR Exception: $e");
      return {
        "Result": "false",
        "ResponseMsg": "Failed to get delivery report: $e"
      };
    }
  }

  /// Send Scheduled SMS
  Future sendScheduledSMS({
    required String mobile,
    required String message,
    required String scheduledTime, // Format: yyyymmddhhmm
  }) async {
    try {
      String apiUrl = "$baseUrl/v2/sendSMS"
          "?username=$username"
          "&apikey=$apiKey"
          "&scheduled=$scheduledTime"
          "&message=${Uri.encodeComponent(message)}"
          "&sendername=$sendername"
          "&smstype=$smstype"
          "&numbers=$mobile";

      var response = await http.get(Uri.parse(apiUrl));

      print("Scheduled SMS Response: ${response.body}");

      if (response.statusCode == 200) {
        var responseData = response.body;
        Map<String, dynamic> parsedResponse =
            _parseResponse(responseData, "", mobile);
        return parsedResponse;
      }

      return {"Result": "false", "ResponseMsg": "Failed to schedule SMS"};
    } catch (e) {
      print("Scheduled SMS Exception: $e");
      return {"Result": "false", "ResponseMsg": "Failed to schedule SMS: $e"};
    }
  }

  /// Test BulkSMSInd API with different sender name configurations
  Future<Map<String, dynamic>> testBulkSMSIndAPI(
      {required String mobile}) async {
    try {
      // Simple test message
      String testMessage = "Test message from MagicMate";

      // Clean mobile number
      String cleanMobile =
          mobile.replaceAll('+', '').replaceAll(' ', '').replaceAll('-', '');

      // Ensure mobile number format
      if (cleanMobile.startsWith('91') && cleanMobile.length == 12) {
        // Good
      } else if (cleanMobile.length == 10) {
        cleanMobile = '91$cleanMobile';
      }

      // Try different sender name configurations
      List<String> senderNames = [
        "", // No sender name
        "BULKSM", // Default
        "INFORM", // Your original
        "SMS", // Simple
        "ALERT", // Common
      ];

      for (String senderName in senderNames) {
        try {
          debugPrint("🧪 Testing with sender: '$senderName'");

          // Build API URL
          String apiUrl = "$baseUrl/v2/sendSMS"
              "?username=$username"
              "&message=${Uri.encodeComponent(testMessage)}"
              "&smstype=$smstype"
              "&numbers=$cleanMobile"
              "&apikey=$apiKey";

          // Add sender name only if not empty
          if (senderName.isNotEmpty) {
            apiUrl += "&sendername=$senderName";
          }

          debugPrint("🧪 Test API URL: $apiUrl");

          var response =
              await http.get(Uri.parse(apiUrl)).timeout(Duration(seconds: 30));

          debugPrint("🧪 Test Response: ${response.body}");
          debugPrint("🧪 Test Status Code: ${response.statusCode}");

          // Parse response to check if successful
          var responseData = response.body.trim();

          // Check if this sender name worked
          if (!responseData.toLowerCase().contains('invalid sendername') &&
              !responseData.toLowerCase().contains('error')) {
            return {
              "Result": "true",
              "ResponseCode": response.statusCode.toString(),
              "ResponseMsg": "Test successful with sender: '$senderName'",
              "response": responseData,
              "workingSender": senderName,
              "status": "success"
            };
          }
        } catch (e) {
          debugPrint("🧪 Test failed for sender '$senderName': $e");
          continue; // Try next sender
        }
      }

      // If all sender names failed
      return {
        "Result": "false",
        "ResponseMsg": "All sender name tests failed",
        "status": "failed"
      };
    } catch (e) {
      debugPrint("🧪 Test Exception: $e");
      return {
        "Result": "false",
        "ResponseMsg": "Test failed: $e",
        "status": "error"
      };
    }
  }

  /// Generate 6-digit OTP
  String _generateOTP() {
    Random random = Random();
    String otp = '';
    for (int i = 0; i < 6; i++) {
      otp += random.nextInt(10).toString();
    }
    return otp;
  }

  /// Parse BulkSMSInd response (usually plain text)
  Map<String, dynamic> _parseResponse(
      String response, String otp, String mobile) {
    try {
      debugPrint("🔍 Raw Response: '$response'");

      // Clean the response
      String cleanResponse = response.trim();

      // BulkSMSInd can return various formats:
      // 1. "Success|MessageID|Status"
      // 2. "Error|ErrorMessage"
      // 3. JSON format
      // 4. Simple success/error text
      // 5. Numeric codes

      // Check if it's JSON
      if (cleanResponse.startsWith('{') && cleanResponse.endsWith('}')) {
        try {
          var jsonResponse = jsonDecode(cleanResponse);
          if (jsonResponse['status'] == 'success' ||
              jsonResponse['success'] == true) {
            return {
              "Result": "true",
              "ResponseCode": "200",
              "ResponseMsg": "OTP sent successfully",
              "otp": otp,
              "msgId":
                  jsonResponse['msgId'] ?? jsonResponse['message_id'] ?? "",
              "status": "sent"
            };
          }
        } catch (e) {
          debugPrint("❌ JSON parsing failed: $e");
        }
      }

      // Check for pipe-separated format
      List<String> parts = cleanResponse.split('|');

      if (parts.isNotEmpty) {
        String firstPart = parts[0].toLowerCase();

        if (firstPart.contains('success') ||
            firstPart.contains('sent') ||
            firstPart == '200') {
          return {
            "Result": "true",
            "ResponseCode": "200",
            "ResponseMsg": "OTP sent successfully",
            "otp": otp,
            "msgId": parts.length > 1 ? parts[1] : "",
            "status": "sent"
          };
        }
      }

      // Check for common success indicators in the response
      String lowerResponse = cleanResponse.toLowerCase();
      if (lowerResponse.contains('success') ||
          lowerResponse.contains('sent') ||
          lowerResponse.contains('delivered') ||
          lowerResponse.contains('accepted') ||
          cleanResponse.contains('200') ||
          cleanResponse.startsWith('1') || // Many SMS APIs return 1 for success
          cleanResponse == 'OK') {
        return {
          "Result": "true",
          "ResponseCode": "200",
          "ResponseMsg": "OTP sent successfully",
          "otp": otp,
          "msgId": cleanResponse,
          "status": "sent"
        };
      }

      // If we reach here, consider it a failure
      return {
        "Result": "false",
        "ResponseCode": "400",
        "ResponseMsg": "SMS sending failed: $cleanResponse",
        "otp": "",
        "msgId": "",
        "status": "failed"
      };
    } catch (e) {
      debugPrint("💥 Parse Exception: $e");
      return {
        "Result": "false",
        "ResponseCode": "500",
        "ResponseMsg": "Failed to parse response: $e",
        "otp": "",
        "msgId": "",
        "status": "error"
      };
    }
  }

  /// Parse Balance Response
  Map<String, dynamic> _parseBalanceResponse(String response) {
    try {
      // Balance response format may vary
      if (response.contains('balance') || response.contains('credit')) {
        return {
          "Result": "true",
          "ResponseCode": "200",
          "ResponseMsg": "Balance retrieved successfully",
          "balance": response,
          "credits": response
        };
      } else {
        return {
          "Result": "false",
          "ResponseCode": "400",
          "ResponseMsg": "Failed to get balance",
          "balance": "0",
          "credits": "0"
        };
      }
    } catch (e) {
      return {
        "Result": "false",
        "ResponseCode": "400",
        "ResponseMsg": "Failed to parse balance response",
        "balance": "0",
        "credits": "0"
      };
    }
  }

  /// Parse DLR Response
  Map<String, dynamic> _parseDlrResponse(String response, String msgId) {
    try {
      return {
        "Result": "true",
        "ResponseCode": "200",
        "ResponseMsg": "DLR retrieved successfully",
        "msgId": msgId,
        "status": response,
        "deliveryTime": DateTime.now().toString(),
        "mobileNumber": ""
      };
    } catch (e) {
      return {
        "Result": "false",
        "ResponseCode": "400",
        "ResponseMsg": "Failed to parse DLR response",
        "msgId": msgId,
        "status": "unknown",
        "deliveryTime": "",
        "mobileNumber": ""
      };
    }
  }
}
