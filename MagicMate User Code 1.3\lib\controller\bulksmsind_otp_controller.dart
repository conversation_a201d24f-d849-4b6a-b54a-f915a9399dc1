import 'dart:convert';
import 'dart:math';

import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:magicmate/Api/config.dart';

import '../model/bulksmsind_otp_model.dart';

class BulkSmsIndOtpController extends GetxController implements GetxService {

  BulkSmsIndOtpModel? bulkSmsIndOtpModel;
  BulkSmsIndBalanceModel? bulkSmsIndBalanceModel;
  BulkSmsIndDlrModel? bulkSmsIndDlrModel;

  // BulkSMSInd API Configuration
  static const String baseUrl = "http://sms.bulksmsind.in443";
  static const String username = "hmescan";
  static const String apiKey = "e0117592-f761-4393-9772-31d4c0eb41cf";
  static const String sendername = "INFORM"; // Your sender name
  static const String smstype = "TRANS"; // TRANS for transactional
  static const String peid = "1701159876885885613"; // Your PE ID from DLT
  static const String templateid = "1707172090686482394"; // Your template ID from DLT

  /// Send OTP SMS via BulkSMSInd API
  Future bulkSmsIndOtpApi({required String mobile}) async {
    try {
      // Generate 6-digit OTP
      String otp = _generateOTP();
      
      // Create OTP message using DLT approved template
      String message = "Your OTP for MagicMate verification is $otp. Valid for 10 minutes. Do not share with anyone.";
      
      // Prepare API URL
      String apiUrl = "$baseUrl/v2/sendSMS"
          "?username=$username"
          "&message=${Uri.encodeComponent(message)}"
          "&sendername=$sendername"
          "&smstype=$smstype"
          "&numbers=$mobile"
          "&apikey=$apiKey"
          "&peid=$peid"
          "&templateid=$templateid";

      print("BulkSMSInd API URL: $apiUrl");
      print("Sending OTP: $otp to mobile: $mobile");

      // Make API call
      var response = await http.get(Uri.parse(apiUrl));
      
      print("BulkSMSInd Response: ${response.body}");
      print("Status Code: ${response.statusCode}");

      if (response.statusCode == 200) {
        // Parse response
        var responseData = response.body;
        
        // BulkSMSInd returns plain text response, parse it
        Map<String, dynamic> parsedResponse = _parseResponse(responseData, otp, mobile);
        
        if (parsedResponse["Result"] == "true") {
          bulkSmsIndOtpModel = BulkSmsIndOtpModel.fromJson(parsedResponse);
          update();
          return parsedResponse;
        } else {
          print("BulkSMSInd Error: ${parsedResponse["ResponseMsg"]}");
          return {
            "Result": "false",
            "ResponseMsg": parsedResponse["ResponseMsg"] ?? "Failed to send OTP"
          };
        }
      } else {
        print("HTTP Error: ${response.statusCode}");
        return {
          "Result": "false",
          "ResponseMsg": "Network error occurred"
        };
      }
    } catch (e) {
      print("BulkSMSInd Exception: $e");
      return {
        "Result": "false",
        "ResponseMsg": "Failed to send OTP: $e"
      };
    }
  }

  /// Check SMS Balance
  Future checkBalance() async {
    try {
      String apiUrl = "$baseUrl/getSMSCredit"
          "?username=$username"
          "&apikey=$apiKey";

      var response = await http.get(Uri.parse(apiUrl));
      
      print("Balance Response: ${response.body}");

      if (response.statusCode == 200) {
        var responseData = response.body;
        Map<String, dynamic> parsedResponse = _parseBalanceResponse(responseData);
        
        if (parsedResponse["Result"] == "true") {
          bulkSmsIndBalanceModel = BulkSmsIndBalanceModel.fromJson(parsedResponse);
          update();
          return parsedResponse;
        }
      }
      
      return {
        "Result": "false",
        "ResponseMsg": "Failed to check balance"
      };
    } catch (e) {
      print("Balance Check Exception: $e");
      return {
        "Result": "false",
        "ResponseMsg": "Failed to check balance: $e"
      };
    }
  }

  /// Get Delivery Report
  Future getDeliveryReport({required String msgId}) async {
    try {
      String apiUrl = "$baseUrl/getDLR"
          "?username=$username"
          "&msgid=$msgId"
          "&apikey=$apiKey";

      var response = await http.get(Uri.parse(apiUrl));
      
      print("DLR Response: ${response.body}");

      if (response.statusCode == 200) {
        var responseData = response.body;
        Map<String, dynamic> parsedResponse = _parseDlrResponse(responseData, msgId);
        
        if (parsedResponse["Result"] == "true") {
          bulkSmsIndDlrModel = BulkSmsIndDlrModel.fromJson(parsedResponse);
          update();
          return parsedResponse;
        }
      }
      
      return {
        "Result": "false",
        "ResponseMsg": "Failed to get delivery report"
      };
    } catch (e) {
      print("DLR Exception: $e");
      return {
        "Result": "false",
        "ResponseMsg": "Failed to get delivery report: $e"
      };
    }
  }

  /// Send Scheduled SMS
  Future sendScheduledSMS({
    required String mobile,
    required String message,
    required String scheduledTime, // Format: yyyymmddhhmm
  }) async {
    try {
      String apiUrl = "$baseUrl/v2/sendSMS"
          "?username=$username"
          "&apikey=$apiKey"
          "&scheduled=$scheduledTime"
          "&message=${Uri.encodeComponent(message)}"
          "&sendername=$sendername"
          "&smstype=$smstype"
          "&numbers=$mobile";

      var response = await http.get(Uri.parse(apiUrl));
      
      print("Scheduled SMS Response: ${response.body}");

      if (response.statusCode == 200) {
        var responseData = response.body;
        Map<String, dynamic> parsedResponse = _parseResponse(responseData, "", mobile);
        return parsedResponse;
      }
      
      return {
        "Result": "false",
        "ResponseMsg": "Failed to schedule SMS"
      };
    } catch (e) {
      print("Scheduled SMS Exception: $e");
      return {
        "Result": "false",
        "ResponseMsg": "Failed to schedule SMS: $e"
      };
    }
  }

  /// Generate 6-digit OTP
  String _generateOTP() {
    Random random = Random();
    String otp = '';
    for (int i = 0; i < 6; i++) {
      otp += random.nextInt(10).toString();
    }
    return otp;
  }

  /// Parse BulkSMSInd response (usually plain text)
  Map<String, dynamic> _parseResponse(String response, String otp, String mobile) {
    try {
      // BulkSMSInd typically returns responses like:
      // "Success|MessageID|Status" or "Error|ErrorMessage"
      
      List<String> parts = response.split('|');
      
      if (parts.isNotEmpty && parts[0].toLowerCase().contains('success')) {
        return {
          "Result": "true",
          "ResponseCode": "200",
          "ResponseMsg": "OTP sent successfully",
          "otp": otp,
          "msgId": parts.length > 1 ? parts[1] : "",
          "status": "sent"
        };
      } else {
        return {
          "Result": "false",
          "ResponseCode": "400",
          "ResponseMsg": parts.length > 1 ? parts[1] : "Failed to send SMS",
          "otp": "",
          "msgId": "",
          "status": "failed"
        };
      }
    } catch (e) {
      // If parsing fails, check if response contains success indicators
      if (response.toLowerCase().contains('success') || 
          response.toLowerCase().contains('sent') ||
          response.contains('200')) {
        return {
          "Result": "true",
          "ResponseCode": "200",
          "ResponseMsg": "OTP sent successfully",
          "otp": otp,
          "msgId": "",
          "status": "sent"
        };
      } else {
        return {
          "Result": "false",
          "ResponseCode": "400",
          "ResponseMsg": "Failed to send SMS",
          "otp": "",
          "msgId": "",
          "status": "failed"
        };
      }
    }
  }

  /// Parse Balance Response
  Map<String, dynamic> _parseBalanceResponse(String response) {
    try {
      // Balance response format may vary
      if (response.contains('balance') || response.contains('credit')) {
        return {
          "Result": "true",
          "ResponseCode": "200",
          "ResponseMsg": "Balance retrieved successfully",
          "balance": response,
          "credits": response
        };
      } else {
        return {
          "Result": "false",
          "ResponseCode": "400",
          "ResponseMsg": "Failed to get balance",
          "balance": "0",
          "credits": "0"
        };
      }
    } catch (e) {
      return {
        "Result": "false",
        "ResponseCode": "400",
        "ResponseMsg": "Failed to parse balance response",
        "balance": "0",
        "credits": "0"
      };
    }
  }

  /// Parse DLR Response
  Map<String, dynamic> _parseDlrResponse(String response, String msgId) {
    try {
      return {
        "Result": "true",
        "ResponseCode": "200",
        "ResponseMsg": "DLR retrieved successfully",
        "msgId": msgId,
        "status": response,
        "deliveryTime": DateTime.now().toString(),
        "mobileNumber": ""
      };
    } catch (e) {
      return {
        "Result": "false",
        "ResponseCode": "400",
        "ResponseMsg": "Failed to parse DLR response",
        "msgId": msgId,
        "status": "unknown",
        "deliveryTime": "",
        "mobileNumber": ""
      };
    }
  }
}
