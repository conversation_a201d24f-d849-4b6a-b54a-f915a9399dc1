// Firebase Collections Setup Script
// Run this in Firebase Console → Firestore → Data → Start collection

// 1. MagicUser Collection
// Collection ID: MagicUser
// Sample Document:
{
  "uid": "1",
  "name": "Test User",
  "email": "<EMAIL>",
  "mobile": "+************",
  "profilePic": "",
  "fcmToken": "sample_fcm_token",
  "isOnline": true,
  "lastActive": new Date(),
  "createdAt": new Date(),
  "appType": "event_user"
}

// 2. MagicOrganizer Collection  
// Collection ID: MagicOrganizer
// Sample Document:
{
  "uid": "1",
  "name": "Test Organizer",
  "email": "<EMAIL>",
  "mobile": "+************",
  "profilePic": "",
  "fcmToken": "sample_fcm_token",
  "isOnline": false,
  "lastActive": new Date(),
  "createdAt": new Date(),
  "appType": "event_organizer"
}

// 3. Magic_Organization_rooms Collection
// Collection ID: Magic_Organization_rooms
// Sample Document ID: U1_O1
{
  "uid": "U1_O1",
  "name": "Test User",
  "email": "<EMAIL>",
  "image": "",
  "isOnline": true,
  "lastActive": new Date(),
  "participants": ["1", "1"],
  "lastMessage": "Hello!",
  "lastMessageTime": new Date()
}

// 4. Magic_Organization_rooms/U1_O1/messages Subcollection
// Collection ID: messages
// Sample Document:
{
  "content": "Hello, I need help with event booking",
  "senderId": "1",
  "receiverId": "1", 
  "sentTime": new Date(),
  "messageType": "text",
  "userType": "US"
}

// 5. events Collection
// Collection ID: events
// Sample Document:
{
  "eventId": "1",
  "title": "Sample Event",
  "description": "This is a sample event",
  "organizerId": "1",
  "startDate": new Date(),
  "endDate": new Date(),
  "location": "Mumbai, India",
  "price": 100,
  "availableTickets": 50,
  "totalTickets": 100,
  "status": "active",
  "createdAt": new Date(),
  "updatedAt": new Date()
}

// 6. bookings Collection
// Collection ID: bookings
// Sample Document:
{
  "bookingId": "1",
  "userId": "1",
  "eventId": "1",
  "organizerId": "1",
  "ticketCount": 2,
  "totalAmount": 200,
  "bookingStatus": "confirmed",
  "paymentStatus": "paid",
  "bookingDate": new Date(),
  "eventDate": new Date()
}

// 7. hotels Collection (for merged app)
// Collection ID: hotels
// Sample Document:
{
  "hotelId": "1",
  "name": "Sample Hotel",
  "description": "A beautiful hotel",
  "location": "Mumbai, India",
  "rating": 4.5,
  "amenities": ["wifi", "parking", "pool"],
  "images": ["https://example.com/image1.jpg"],
  "pricePerNight": 2000,
  "availableRooms": 10,
  "totalRooms": 20,
  "status": "active"
}

// 8. hotel_bookings Collection
// Collection ID: hotel_bookings
// Sample Document:
{
  "bookingId": "1",
  "userId": "1",
  "hotelId": "1",
  "checkInDate": new Date(),
  "checkOutDate": new Date(),
  "roomCount": 1,
  "guestCount": 2,
  "totalAmount": 4000,
  "bookingStatus": "confirmed",
  "paymentStatus": "paid"
}

// 9. notifications Collection
// Collection ID: notifications
// Sample Document:
{
  "userId": "1",
  "title": "Booking Confirmed",
  "body": "Your event booking has been confirmed",
  "type": "event_booking",
  "data": {
    "eventId": "1",
    "bookingId": "1"
  },
  "sentAt": new Date(),
  "readAt": null,
  "status": "sent"
}

// FIRESTORE SECURITY RULES
// Copy this to Firebase Console → Firestore → Rules:

/*
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // MagicUser collection
    match /MagicUser/{userId} {
      allow read, write: if request.auth != null;
    }
    
    // MagicOrganizer collection
    match /MagicOrganizer/{organizerId} {
      allow read, write: if request.auth != null;
    }
    
    // Magic_Organization_rooms collection
    match /Magic_Organization_rooms/{roomId} {
      allow read, write: if request.auth != null;
      
      match /messages/{messageId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // Events collection
    match /events/{eventId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // Bookings collection
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null;
    }
    
    // Hotels collection
    match /hotels/{hotelId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // Hotel bookings collection
    match /hotel_bookings/{bookingId} {
      allow read, write: if request.auth != null;
    }
    
    // Notifications collection
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null;
    }
  }
}
*/

// FIREBASE STORAGE RULES
// Copy this to Firebase Console → Storage → Rules:

/*
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
*/

// CLOUD MESSAGING SETUP
// 1. Go to Firebase Console → Project Settings → Cloud Messaging
// 2. Enable Cloud Messaging API
// 3. Copy Server Key for backend notifications
// 4. Add to your backend configuration

// AUTHENTICATION SETUP
// 1. Go to Firebase Console → Authentication → Sign-in method
// 2. Enable Email/Password
// 3. Enable Phone (for OTP functionality)
// 4. Configure authorized domains if needed

console.log("Firebase Collections Setup Complete!");
console.log("Project ID: linkinblink-f544a");
console.log("Remember to:");
console.log("1. Create all collections with sample documents");
console.log("2. Set up security rules");
console.log("3. Configure Cloud Messaging");
console.log("4. Enable Authentication methods");
console.log("5. Set up Storage with rules");
