import 'dart:convert';

BulkSmsIndOtpModel bulkSmsIndOtpModelFromJson(String str) => BulkSmsIndOtpModel.fromJson(json.decode(str));

String bulkSmsIndOtpModelToJson(BulkSmsIndOtpModel data) => json.encode(data.toJson());

class BulkSmsIndOtpModel {
  String? result;
  String? responseCode;
  String? responseMsg;
  String? otp;
  String? msgId;
  String? status;

  BulkSmsIndOtpModel({
    this.result,
    this.responseCode,
    this.responseMsg,
    this.otp,
    this.msgId,
    this.status,
  });

  factory BulkSmsIndOtpModel.fromJson(Map<String, dynamic> json) => BulkSmsIndOtpModel(
    result: json["Result"],
    responseCode: json["ResponseCode"],
    responseMsg: json["ResponseMsg"],
    otp: json["otp"],
    msgId: json["msgId"],
    status: json["status"],
  );

  Map<String, dynamic> toJson() => {
    "Result": result,
    "ResponseCode": responseCode,
    "ResponseMsg": responseMsg,
    "otp": otp,
    "msgId": msgId,
    "status": status,
  };
}

// Balance Check Model
BulkSmsIndBalanceModel bulkSmsIndBalanceModelFromJson(String str) => BulkSmsIndBalanceModel.fromJson(json.decode(str));

String bulkSmsIndBalanceModelToJson(BulkSmsIndBalanceModel data) => json.encode(data.toJson());

class BulkSmsIndBalanceModel {
  String? result;
  String? responseCode;
  String? responseMsg;
  String? balance;
  String? credits;

  BulkSmsIndBalanceModel({
    this.result,
    this.responseCode,
    this.responseMsg,
    this.balance,
    this.credits,
  });

  factory BulkSmsIndBalanceModel.fromJson(Map<String, dynamic> json) => BulkSmsIndBalanceModel(
    result: json["Result"],
    responseCode: json["ResponseCode"],
    responseMsg: json["ResponseMsg"],
    balance: json["balance"],
    credits: json["credits"],
  );

  Map<String, dynamic> toJson() => {
    "Result": result,
    "ResponseCode": responseCode,
    "ResponseMsg": responseMsg,
    "balance": balance,
    "credits": credits,
  };
}

// Delivery Report Model
BulkSmsIndDlrModel bulkSmsIndDlrModelFromJson(String str) => BulkSmsIndDlrModel.fromJson(json.decode(str));

String bulkSmsIndDlrModelToJson(BulkSmsIndDlrModel data) => json.encode(data.toJson());

class BulkSmsIndDlrModel {
  String? result;
  String? responseCode;
  String? responseMsg;
  String? msgId;
  String? status;
  String? deliveryTime;
  String? mobileNumber;

  BulkSmsIndDlrModel({
    this.result,
    this.responseCode,
    this.responseMsg,
    this.msgId,
    this.status,
    this.deliveryTime,
    this.mobileNumber,
  });

  factory BulkSmsIndDlrModel.fromJson(Map<String, dynamic> json) => BulkSmsIndDlrModel(
    result: json["Result"],
    responseCode: json["ResponseCode"],
    responseMsg: json["ResponseMsg"],
    msgId: json["msgId"],
    status: json["status"],
    deliveryTime: json["deliveryTime"],
    mobileNumber: json["mobileNumber"],
  );

  Map<String, dynamic> toJson() => {
    "Result": result,
    "ResponseCode": responseCode,
    "ResponseMsg": responseMsg,
    "msgId": msgId,
    "status": status,
    "deliveryTime": deliveryTime,
    "mobileNumber": mobileNumber,
  };
}
