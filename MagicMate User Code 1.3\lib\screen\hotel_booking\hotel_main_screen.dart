import 'package:flutter/material.dart';
import 'package:magicmate/utils/Colors.dart';
import 'package:magicmate/screen/hotel_booking/hotel_explore_screen.dart';
import 'package:magicmate/screen/hotel_booking/bulksmsind_test_screen.dart';

class HotelMainScreen extends StatefulWidget {
  const HotelMainScreen({super.key});

  @override
  State<HotelMainScreen> createState() => _HotelMainScreenState();
}

class _HotelMainScreenState extends State<HotelMainScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: const HotelExploreScreen(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const BulkSmsIndTestScreen(),
            ),
          );
        },
        backgroundColor: Colors.red,
        child: Icon(Icons.sms, color: Colors.white),
        tooltip: 'Test BulkSMSInd API',
      ),
    );
  }
}
