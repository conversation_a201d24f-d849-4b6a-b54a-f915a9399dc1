<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final System Check</title>
    <link rel="stylesheet" type="text/css" href="assets/css/vendors/bootstrap.css">
    <style>
        body { background: #f8f9fa; padding: 20px; font-family: Arial, sans-serif; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .check-item { padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #ccc; }
        .check-success { background: #d4edda; border-left-color: #28a745; color: #155724; }
        .check-error { background: #f8d7da; border-left-color: #dc3545; color: #721c24; }
        .check-warning { background: #fff3cd; border-left-color: #ffc107; color: #856404; }
        .check-info { background: #d1ecf1; border-left-color: #17a2b8; color: #0c5460; }
        .section { margin: 30px 0; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Final System Check - Admin Panel Ready?</h1>
        
        <?php
        session_start();
        
        // Include configuration
        try {
            include 'filemanager/evconfing.php';
            $config_loaded = true;
        } catch (Exception $e) {
            $config_loaded = false;
            $config_error = $e->getMessage();
        }
        ?>
        
        <div class="section">
            <h2>1. 🔧 Core System Components</h2>
            
            <?php if ($config_loaded): ?>
                <div class="check-success">✅ Configuration loaded successfully</div>
            <?php else: ?>
                <div class="check-error">❌ Configuration failed: <?php echo $config_error; ?></div>
            <?php endif; ?>
            
            <?php
            $core_files = [
                'index.php' => 'Login page',
                'dashboard.php' => 'Main dashboard',
                'setting.php' => 'Settings page',
                'filemanager/head.php' => 'Header component',
                'filemanager/navbar.php' => 'Navigation bar',
                'filemanager/sidebar.php' => 'Sidebar menu',
                'filemanager/script.php' => 'JavaScript loader',
                'assets/css/style.css' => 'Main stylesheet',
                'assets/js/script.js' => 'Main JavaScript'
            ];
            
            foreach ($core_files as $file => $description) {
                if (file_exists($file)) {
                    echo "<div class='check-success'>✅ $description ($file)</div>";
                } else {
                    echo "<div class='check-error'>❌ Missing: $description ($file)</div>";
                }
            }
            ?>
        </div>
        
        <div class="section">
            <h2>2. 🗄️ Database Connectivity</h2>
            
            <?php if ($config_loaded && isset($evmulti)): ?>
                <?php if ($evmulti->connect_error): ?>
                    <div class="check-error">❌ Database connection failed: <?php echo $evmulti->connect_error; ?></div>
                <?php else: ?>
                    <div class="check-success">✅ Database connected successfully</div>
                    
                    <?php
                    // Check essential tables
                    $tables = [
                        'tbl_admin' => 'Admin users',
                        'tbl_setting' => 'System settings',
                        'tbl_event' => 'Events data',
                        'tbl_sponsore' => 'Organizers data'
                    ];
                    
                    foreach ($tables as $table => $description) {
                        $result = $evmulti->query("SHOW TABLES LIKE '$table'");
                        if ($result && $result->num_rows > 0) {
                            $count_result = $evmulti->query("SELECT COUNT(*) as count FROM $table");
                            $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
                            echo "<div class='check-success'>✅ $description table exists ($count records)</div>";
                        } else {
                            echo "<div class='check-error'>❌ Missing table: $table ($description)</div>";
                        }
                    }
                    ?>
                <?php endif; ?>
            <?php else: ?>
                <div class="check-error">❌ Database configuration not loaded</div>
            <?php endif; ?>
        </div>
        
        <div class="section">
            <h2>3. 🔐 Authentication System</h2>
            
            <?php if (isset($_SESSION["evename"])): ?>
                <div class="check-success">✅ User logged in: <?php echo $_SESSION["evename"]; ?></div>
                <div class="check-success">✅ User type: <?php echo $_SESSION["stype"]; ?></div>
                <div class="check-info">🔗 <a href="dashboard.php" class="btn btn-success">Go to Dashboard</a></div>
            <?php else: ?>
                <div class="check-warning">⚠️ No active session - need to login</div>
                <div class="check-info">🔗 <a href="index.php" class="btn btn-primary">Go to Login</a></div>
            <?php endif; ?>
        </div>
        
        <div class="section">
            <h2>4. 📱 SMS Integration (BulkSMSInd)</h2>
            
            <?php
            $sms_files = [
                'user_api/bulksmsind_otp.php' => 'User SMS API',
                'orag_api/bulksmsind_otp.php' => 'Organizer SMS API',
                'user_api/sms_type.php' => 'SMS Type API'
            ];
            
            foreach ($sms_files as $file => $description) {
                if (file_exists($file)) {
                    echo "<div class='check-success'>✅ $description ($file)</div>";
                } else {
                    echo "<div class='check-error'>❌ Missing: $description ($file)</div>";
                }
            }
            
            // Check SMS settings
            if ($config_loaded && isset($set)) {
                if (isset($set['sms_type'])) {
                    echo "<div class='check-info'>📊 Current SMS Type: " . $set['sms_type'] . "</div>";
                    
                    if ($set['sms_type'] == 'BulkSMSInd') {
                        echo "<div class='check-success'>✅ BulkSMSInd is configured as SMS provider</div>";
                    } else {
                        echo "<div class='check-warning'>⚠️ SMS provider is set to: " . $set['sms_type'] . " (not BulkSMSInd)</div>";
                    }
                } else {
                    echo "<div class='check-warning'>⚠️ SMS type not configured</div>";
                }
            }
            ?>
        </div>
        
        <div class="section">
            <h2>5. 🎨 Frontend Assets</h2>
            
            <?php
            $asset_dirs = [
                'assets/css' => 'CSS files',
                'assets/js' => 'JavaScript files',
                'assets/css/missing-images-fix.css' => 'Image fix CSS',
                'assets/js/emergency-loader-fix.js' => 'Loader fix JS',
                'images' => 'Images directory'
            ];
            
            foreach ($asset_dirs as $path => $description) {
                if (file_exists($path)) {
                    echo "<div class='check-success'>✅ $description ($path)</div>";
                } else {
                    echo "<div class='check-error'>❌ Missing: $description ($path)</div>";
                }
            }
            ?>
        </div>
        
        <div class="section">
            <h2>6. 🚀 System Status Summary</h2>
            
            <?php
            $all_good = true;
            $issues = [];
            
            // Check critical components
            if (!$config_loaded) { $all_good = false; $issues[] = "Configuration not loaded"; }
            if (!file_exists('dashboard.php')) { $all_good = false; $issues[] = "Dashboard missing"; }
            if (!file_exists('index.php')) { $all_good = false; $issues[] = "Login page missing"; }
            if (isset($evmulti) && $evmulti->connect_error) { $all_good = false; $issues[] = "Database connection failed"; }
            
            if ($all_good): ?>
                <div class="check-success">
                    <h3>🎉 SYSTEM READY FOR PRODUCTION!</h3>
                    <p><strong>All critical components are working:</strong></p>
                    <ul>
                        <li>✅ Login system functional</li>
                        <li>✅ Dashboard will load properly</li>
                        <li>✅ Database connectivity established</li>
                        <li>✅ SMS integration ready</li>
                        <li>✅ Frontend assets loaded</li>
                        <li>✅ Domain validation removed</li>
                    </ul>
                    <p><strong>Next Steps:</strong></p>
                    <ol>
                        <li>Login to admin panel</li>
                        <li>Configure BulkSMSInd in settings</li>
                        <li>Test SMS functionality</li>
                        <li>Deploy to production</li>
                    </ol>
                </div>
            <?php else: ?>
                <div class="check-error">
                    <h3>⚠️ ISSUES FOUND - NEEDS ATTENTION</h3>
                    <p><strong>The following issues need to be resolved:</strong></p>
                    <ul>
                        <?php foreach ($issues as $issue): ?>
                            <li>❌ <?php echo $issue; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="section">
            <h2>7. 🔗 Quick Actions</h2>
            <a href="index.php" class="btn btn-primary">Login Page</a>
            <a href="dashboard.php" class="btn btn-success">Dashboard</a>
            <a href="setting.php" class="btn btn-warning">Settings</a>
            <a href="test_login_flow.php" class="btn btn-danger">Test Login</a>
        </div>
    </div>
</body>
</html>
