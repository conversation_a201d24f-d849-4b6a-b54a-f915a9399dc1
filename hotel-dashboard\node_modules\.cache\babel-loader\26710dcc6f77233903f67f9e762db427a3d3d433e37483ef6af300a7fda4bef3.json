{"ast": null, "code": "var _jsxFileName = \"G:\\\\linkinblink\\\\hotel-dashboard\\\\src\\\\components\\\\notifications\\\\ServiceRequestNotification.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Badge, Box, Button, Card, CardContent, Divider, IconButton, List, ListItem, ListItemAvatar, ListItemText, Menu, MenuItem, Popover, Tooltip, Typography } from '@mui/material';\nimport { RoomService as RoomServiceIcon, CleaningServices as CleaningIcon, Build as BuildIcon, Restaurant as RestaurantIcon, MoreVert as MoreVertIcon, CheckCircle as CheckCircleIcon, AccessTime as AccessTimeIcon } from '@mui/icons-material';\nimport { collection, query, where, orderBy, onSnapshot, updateDoc, doc } from 'firebase/firestore';\nimport { db } from '../../firebase/config';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { formatDistanceToNow } from 'date-fns';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ServiceRequestNotification = () => {\n  _s();\n  const [notifications, setNotifications] = useState([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [menuAnchorEl, setMenuAnchorEl] = useState(null);\n  const [selectedNotification, setSelectedNotification] = useState(null);\n  const {\n    currentUser\n  } = useAuth();\n  const navigate = useNavigate();\n  const open = Boolean(anchorEl);\n  const menuOpen = Boolean(menuAnchorEl);\n  useEffect(() => {\n    if (!currentUser) return;\n\n    // Query notifications for the current vendor\n    const notificationsQuery = query(collection(db, 'mobileNotifications'), where('userId', '==', currentUser.uid), where('type', '==', 'serviceRequest'), orderBy('createdAt', 'desc'));\n    const unsubscribe = onSnapshot(notificationsQuery, snapshot => {\n      const notificationsList = [];\n      snapshot.forEach(doc => {\n        const data = doc.data();\n        notificationsList.push({\n          id: doc.id,\n          title: data.title || 'Service Request',\n          message: data.message || '',\n          type: data.type || 'serviceRequest',\n          read: data.read || false,\n          hotelId: data.hotelId || '',\n          actionUrl: data.actionUrl || '',\n          createdAt: data.createdAt\n        });\n      });\n      setNotifications(notificationsList);\n      setUnreadCount(notificationsList.filter(n => !n.read).length);\n    });\n    return () => unsubscribe();\n  }, [currentUser]);\n  const handleClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n  const handleMenuClick = (event, notification) => {\n    event.stopPropagation();\n    setSelectedNotification(notification);\n    setMenuAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = () => {\n    setMenuAnchorEl(null);\n  };\n  const handleNotificationClick = async notification => {\n    // Mark as read\n    if (!notification.read) {\n      await updateDoc(doc(db, 'mobileNotifications', notification.id), {\n        read: true\n      });\n    }\n\n    // Navigate to the action URL if provided\n    if (notification.actionUrl) {\n      navigate(notification.actionUrl);\n    }\n    handleClose();\n  };\n  const markAsRead = async notification => {\n    await updateDoc(doc(db, 'mobileNotifications', notification.id), {\n      read: true\n    });\n    handleMenuClose();\n  };\n  const markAllAsRead = async () => {\n    const promises = notifications.filter(n => !n.read).map(n => updateDoc(doc(db, 'mobileNotifications', n.id), {\n      read: true\n    }));\n    await Promise.all(promises);\n    handleMenuClose();\n  };\n  const getNotificationIcon = type => {\n    if (type.includes('food')) {\n      return /*#__PURE__*/_jsxDEV(RestaurantIcon, {\n        color: \"primary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 14\n      }, this);\n    } else if (type.includes('cleaning')) {\n      return /*#__PURE__*/_jsxDEV(CleaningIcon, {\n        color: \"secondary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 14\n      }, this);\n    } else if (type.includes('maintenance')) {\n      return /*#__PURE__*/_jsxDEV(BuildIcon, {\n        color: \"warning\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 14\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(RoomServiceIcon, {\n        color: \"info\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 14\n      }, this);\n    }\n  };\n  const getTimeAgo = timestamp => {\n    if (!timestamp) return 'Just now';\n    try {\n      const date = timestamp.toDate();\n      return formatDistanceToNow(date, {\n        addSuffix: true\n      });\n    } catch (error) {\n      return 'Just now';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n      title: \"Service Requests\",\n      children: /*#__PURE__*/_jsxDEV(IconButton, {\n        color: \"inherit\",\n        onClick: handleClick,\n        sx: {\n          ml: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Badge, {\n          badgeContent: unreadCount,\n          color: \"error\",\n          children: /*#__PURE__*/_jsxDEV(RoomServiceIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      open: open,\n      anchorEl: anchorEl,\n      onClose: handleClose,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      transformOrigin: {\n        vertical: 'top',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          width: 360,\n          maxHeight: 480\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 2,\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Service Requests\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(Button, {\n            size: \"small\",\n            onClick: markAllAsRead,\n            children: \"Mark all as read\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), notifications.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 4,\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"textSecondary\",\n            children: \"No service requests\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: 360,\n            overflow: 'auto'\n          },\n          children: notifications.map(notification => /*#__PURE__*/_jsxDEV(ListItem, {\n            alignItems: \"flex-start\",\n            sx: {\n              bgcolor: notification.read ? 'transparent' : 'action.hover',\n              '&:hover': {\n                bgcolor: 'action.selected'\n              },\n              cursor: 'pointer'\n            },\n            onClick: () => handleNotificationClick(notification),\n            secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n              edge: \"end\",\n              onClick: e => handleMenuClick(e, notification),\n              children: /*#__PURE__*/_jsxDEV(MoreVertIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 21\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n              children: getNotificationIcon(notification.title.toLowerCase())\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: notification.title,\n              secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  component: \"span\",\n                  variant: \"body2\",\n                  color: \"textPrimary\",\n                  sx: {\n                    display: 'block'\n                  },\n                  children: notification.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  component: \"span\",\n                  variant: \"caption\",\n                  color: \"textSecondary\",\n                  children: getTimeAgo(notification.createdAt)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 19\n            }, this)]\n          }, notification.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"text\",\n            onClick: () => {\n              navigate('/vendor/services');\n              handleClose();\n            },\n            children: \"View All Service Requests\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: menuAnchorEl,\n      open: menuOpen,\n      onClose: handleMenuClose,\n      children: [selectedNotification && !selectedNotification.read && /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => markAsRead(selectedNotification),\n        children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          fontSize: \"small\",\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this), \"Mark as read\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleMenuClose,\n        children: [/*#__PURE__*/_jsxDEV(AccessTimeIcon, {\n          fontSize: \"small\",\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), \"Snooze notification\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ServiceRequestNotification, \"T+UErZCqPqCk4dJRwgcGe5yEoqU=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = ServiceRequestNotification;\nexport default ServiceRequestNotification;\nvar _c;\n$RefreshReg$(_c, \"ServiceRequestNotification\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Badge", "Box", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "IconButton", "List", "ListItem", "ListItemAvatar", "ListItemText", "<PERSON><PERSON>", "MenuItem", "Popover", "<PERSON><PERSON><PERSON>", "Typography", "RoomService", "RoomServiceIcon", "CleaningServices", "CleaningIcon", "Build", "BuildIcon", "Restaurant", "RestaurantIcon", "<PERSON><PERSON><PERSON>", "MoreVertIcon", "CheckCircle", "CheckCircleIcon", "AccessTime", "AccessTimeIcon", "collection", "query", "where", "orderBy", "onSnapshot", "updateDoc", "doc", "db", "useAuth", "useNavigate", "formatDistanceToNow", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ServiceRequestNotification", "_s", "notifications", "setNotifications", "unreadCount", "setUnreadCount", "anchorEl", "setAnchorEl", "menuAnchorEl", "setMenuAnchorEl", "selectedNotification", "setSelectedNotification", "currentUser", "navigate", "open", "Boolean", "menuOpen", "notificationsQuery", "uid", "unsubscribe", "snapshot", "notificationsList", "for<PERSON>ach", "data", "push", "id", "title", "message", "type", "read", "hotelId", "actionUrl", "createdAt", "filter", "n", "length", "handleClick", "event", "currentTarget", "handleClose", "handleMenuClick", "notification", "stopPropagation", "handleMenuClose", "handleNotificationClick", "mark<PERSON><PERSON><PERSON>", "markAllAsRead", "promises", "map", "Promise", "all", "getNotificationIcon", "includes", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getTimeAgo", "timestamp", "date", "toDate", "addSuffix", "error", "children", "onClick", "sx", "ml", "badgeContent", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "width", "maxHeight", "p", "display", "justifyContent", "alignItems", "variant", "size", "textAlign", "overflow", "bgcolor", "cursor", "secondaryAction", "edge", "e", "toLowerCase", "primary", "secondary", "component", "fullWidth", "fontSize", "mr", "_c", "$RefreshReg$"], "sources": ["G:/linkinblink/hotel-dashboard/src/components/notifications/ServiceRequestNotification.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON>,\n  <PERSON>,\n  CardContent,\n  Divider,\n  IconButton,\n  List,\n  ListItem,\n  ListItemAvatar,\n  ListItemText,\n  Menu,\n  MenuItem,\n  Popover,\n  Tooltip,\n  Typography\n} from '@mui/material';\nimport {\n  Notifications as NotificationsIcon,\n  RoomService as RoomServiceIcon,\n  CleaningServices as CleaningIcon,\n  Build as BuildIcon,\n  Restaurant as RestaurantIcon,\n  MoreVert as MoreVertIcon,\n  CheckCircle as CheckCircleIcon,\n  Cancel as CancelIcon,\n  AccessTime as AccessTimeIcon\n} from '@mui/icons-material';\nimport { collection, query, where, orderBy, onSnapshot, updateDoc, doc } from 'firebase/firestore';\nimport { db } from '../../firebase/config';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { formatDistanceToNow } from 'date-fns';\n\ninterface Notification {\n  id: string;\n  title: string;\n  message: string;\n  type: string;\n  read: boolean;\n  hotelId: string;\n  actionUrl?: string;\n  createdAt: any;\n}\n\nconst ServiceRequestNotification: React.FC = () => {\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\n  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);\n  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);\n  const { currentUser } = useAuth();\n  const navigate = useNavigate();\n\n  const open = Boolean(anchorEl);\n  const menuOpen = Boolean(menuAnchorEl);\n\n  useEffect(() => {\n    if (!currentUser) return;\n\n    // Query notifications for the current vendor\n    const notificationsQuery = query(\n      collection(db, 'mobileNotifications'),\n      where('userId', '==', currentUser.uid),\n      where('type', '==', 'serviceRequest'),\n      orderBy('createdAt', 'desc')\n    );\n\n    const unsubscribe = onSnapshot(notificationsQuery, (snapshot) => {\n      const notificationsList: Notification[] = [];\n      snapshot.forEach((doc) => {\n        const data = doc.data();\n        notificationsList.push({\n          id: doc.id,\n          title: data.title || 'Service Request',\n          message: data.message || '',\n          type: data.type || 'serviceRequest',\n          read: data.read || false,\n          hotelId: data.hotelId || '',\n          actionUrl: data.actionUrl || '',\n          createdAt: data.createdAt,\n        });\n      });\n\n      setNotifications(notificationsList);\n      setUnreadCount(notificationsList.filter(n => !n.read).length);\n    });\n\n    return () => unsubscribe();\n  }, [currentUser]);\n\n  const handleClick = (event: React.MouseEvent<HTMLElement>) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n\n  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, notification: Notification) => {\n    event.stopPropagation();\n    setSelectedNotification(notification);\n    setMenuAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setMenuAnchorEl(null);\n  };\n\n  const handleNotificationClick = async (notification: Notification) => {\n    // Mark as read\n    if (!notification.read) {\n      await updateDoc(doc(db, 'mobileNotifications', notification.id), {\n        read: true\n      });\n    }\n\n    // Navigate to the action URL if provided\n    if (notification.actionUrl) {\n      navigate(notification.actionUrl);\n    }\n\n    handleClose();\n  };\n\n  const markAsRead = async (notification: Notification) => {\n    await updateDoc(doc(db, 'mobileNotifications', notification.id), {\n      read: true\n    });\n    handleMenuClose();\n  };\n\n  const markAllAsRead = async () => {\n    const promises = notifications\n      .filter(n => !n.read)\n      .map(n => updateDoc(doc(db, 'mobileNotifications', n.id), { read: true }));\n\n    await Promise.all(promises);\n    handleMenuClose();\n  };\n\n  const getNotificationIcon = (type: string) => {\n    if (type.includes('food')) {\n      return <RestaurantIcon color=\"primary\" />;\n    } else if (type.includes('cleaning')) {\n      return <CleaningIcon color=\"secondary\" />;\n    } else if (type.includes('maintenance')) {\n      return <BuildIcon color=\"warning\" />;\n    } else {\n      return <RoomServiceIcon color=\"info\" />;\n    }\n  };\n\n  const getTimeAgo = (timestamp: any) => {\n    if (!timestamp) return 'Just now';\n\n    try {\n      const date = timestamp.toDate();\n      return formatDistanceToNow(date, { addSuffix: true });\n    } catch (error) {\n      return 'Just now';\n    }\n  };\n\n  return (\n    <>\n      <Tooltip title=\"Service Requests\">\n        <IconButton\n          color=\"inherit\"\n          onClick={handleClick}\n          sx={{ ml: 1 }}\n        >\n          <Badge badgeContent={unreadCount} color=\"error\">\n            <RoomServiceIcon />\n          </Badge>\n        </IconButton>\n      </Tooltip>\n\n      <Popover\n        open={open}\n        anchorEl={anchorEl}\n        onClose={handleClose}\n        anchorOrigin={{\n          vertical: 'bottom',\n          horizontal: 'right',\n        }}\n        transformOrigin={{\n          vertical: 'top',\n          horizontal: 'right',\n        }}\n      >\n        <Card sx={{ width: 360, maxHeight: 480 }}>\n          <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">Service Requests</Typography>\n            {unreadCount > 0 && (\n              <Button size=\"small\" onClick={markAllAsRead}>\n                Mark all as read\n              </Button>\n            )}\n          </Box>\n          <Divider />\n\n          {notifications.length === 0 ? (\n            <Box sx={{ p: 4, textAlign: 'center' }}>\n              <Typography color=\"textSecondary\">No service requests</Typography>\n            </Box>\n          ) : (\n            <List sx={{ maxHeight: 360, overflow: 'auto' }}>\n              {notifications.map((notification) => (\n                <ListItem\n                  key={notification.id}\n                  alignItems=\"flex-start\"\n                  sx={{\n                    bgcolor: notification.read ? 'transparent' : 'action.hover',\n                    '&:hover': { bgcolor: 'action.selected' },\n                    cursor: 'pointer',\n                  }}\n                  onClick={() => handleNotificationClick(notification)}\n                  secondaryAction={\n                    <IconButton edge=\"end\" onClick={(e) => handleMenuClick(e, notification)}>\n                      <MoreVertIcon />\n                    </IconButton>\n                  }\n                >\n                  <ListItemAvatar>\n                    {getNotificationIcon(notification.title.toLowerCase())}\n                  </ListItemAvatar>\n                  <ListItemText\n                    primary={notification.title}\n                    secondary={\n                      <>\n                        <Typography\n                          component=\"span\"\n                          variant=\"body2\"\n                          color=\"textPrimary\"\n                          sx={{ display: 'block' }}\n                        >\n                          {notification.message}\n                        </Typography>\n                        <Typography\n                          component=\"span\"\n                          variant=\"caption\"\n                          color=\"textSecondary\"\n                        >\n                          {getTimeAgo(notification.createdAt)}\n                        </Typography>\n                      </>\n                    }\n                  />\n                </ListItem>\n              ))}\n            </List>\n          )}\n\n          <Divider />\n          <CardContent>\n            <Button\n              fullWidth\n              variant=\"text\"\n              onClick={() => {\n                navigate('/vendor/services');\n                handleClose();\n              }}\n            >\n              View All Service Requests\n            </Button>\n          </CardContent>\n        </Card>\n      </Popover>\n\n      <Menu\n        anchorEl={menuAnchorEl}\n        open={menuOpen}\n        onClose={handleMenuClose}\n      >\n        {selectedNotification && !selectedNotification.read && (\n          <MenuItem onClick={() => markAsRead(selectedNotification)}>\n            <CheckCircleIcon fontSize=\"small\" sx={{ mr: 1 }} />\n            Mark as read\n          </MenuItem>\n        )}\n        <MenuItem onClick={handleMenuClose}>\n          <AccessTimeIcon fontSize=\"small\" sx={{ mr: 1 }} />\n          Snooze notification\n        </MenuItem>\n      </Menu>\n    </>\n  );\n};\n\nexport default ServiceRequestNotification;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,IAAI,EACJC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,UAAU,QACL,eAAe;AACtB,SAEEC,WAAW,IAAIC,eAAe,EAC9BC,gBAAgB,IAAIC,YAAY,EAChCC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,eAAe,EAE9BC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,GAAG,QAAQ,oBAAoB;AAClG,SAASC,EAAE,QAAQ,uBAAuB;AAC1C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,mBAAmB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAa/C,MAAMC,0BAAoC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAqB,IAAI,CAAC;EAC1E,MAAM,CAACyD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1D,QAAQ,CAAsB,IAAI,CAAC;EAC3F,MAAM;IAAE2D;EAAY,CAAC,GAAGnB,OAAO,CAAC,CAAC;EACjC,MAAMoB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAE9B,MAAMoB,IAAI,GAAGC,OAAO,CAACT,QAAQ,CAAC;EAC9B,MAAMU,QAAQ,GAAGD,OAAO,CAACP,YAAY,CAAC;EAEtCtD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0D,WAAW,EAAE;;IAElB;IACA,MAAMK,kBAAkB,GAAG/B,KAAK,CAC9BD,UAAU,CAACO,EAAE,EAAE,qBAAqB,CAAC,EACrCL,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAEyB,WAAW,CAACM,GAAG,CAAC,EACtC/B,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,gBAAgB,CAAC,EACrCC,OAAO,CAAC,WAAW,EAAE,MAAM,CAC7B,CAAC;IAED,MAAM+B,WAAW,GAAG9B,UAAU,CAAC4B,kBAAkB,EAAGG,QAAQ,IAAK;MAC/D,MAAMC,iBAAiC,GAAG,EAAE;MAC5CD,QAAQ,CAACE,OAAO,CAAE/B,GAAG,IAAK;QACxB,MAAMgC,IAAI,GAAGhC,GAAG,CAACgC,IAAI,CAAC,CAAC;QACvBF,iBAAiB,CAACG,IAAI,CAAC;UACrBC,EAAE,EAAElC,GAAG,CAACkC,EAAE;UACVC,KAAK,EAAEH,IAAI,CAACG,KAAK,IAAI,iBAAiB;UACtCC,OAAO,EAAEJ,IAAI,CAACI,OAAO,IAAI,EAAE;UAC3BC,IAAI,EAAEL,IAAI,CAACK,IAAI,IAAI,gBAAgB;UACnCC,IAAI,EAAEN,IAAI,CAACM,IAAI,IAAI,KAAK;UACxBC,OAAO,EAAEP,IAAI,CAACO,OAAO,IAAI,EAAE;UAC3BC,SAAS,EAAER,IAAI,CAACQ,SAAS,IAAI,EAAE;UAC/BC,SAAS,EAAET,IAAI,CAACS;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF7B,gBAAgB,CAACkB,iBAAiB,CAAC;MACnChB,cAAc,CAACgB,iBAAiB,CAACY,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACL,IAAI,CAAC,CAACM,MAAM,CAAC;IAC/D,CAAC,CAAC;IAEF,OAAO,MAAMhB,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACP,WAAW,CAAC,CAAC;EAEjB,MAAMwB,WAAW,GAAIC,KAAoC,IAAK;IAC5D9B,WAAW,CAAC8B,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBhC,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMiC,eAAe,GAAGA,CAACH,KAAoC,EAAEI,YAA0B,KAAK;IAC5FJ,KAAK,CAACK,eAAe,CAAC,CAAC;IACvB/B,uBAAuB,CAAC8B,YAAY,CAAC;IACrChC,eAAe,CAAC4B,KAAK,CAACC,aAAa,CAAC;EACtC,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5BlC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMmC,uBAAuB,GAAG,MAAOH,YAA0B,IAAK;IACpE;IACA,IAAI,CAACA,YAAY,CAACZ,IAAI,EAAE;MACtB,MAAMvC,SAAS,CAACC,GAAG,CAACC,EAAE,EAAE,qBAAqB,EAAEiD,YAAY,CAAChB,EAAE,CAAC,EAAE;QAC/DI,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIY,YAAY,CAACV,SAAS,EAAE;MAC1BlB,QAAQ,CAAC4B,YAAY,CAACV,SAAS,CAAC;IAClC;IAEAQ,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMM,UAAU,GAAG,MAAOJ,YAA0B,IAAK;IACvD,MAAMnD,SAAS,CAACC,GAAG,CAACC,EAAE,EAAE,qBAAqB,EAAEiD,YAAY,CAAChB,EAAE,CAAC,EAAE;MAC/DI,IAAI,EAAE;IACR,CAAC,CAAC;IACFc,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAMG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAMC,QAAQ,GAAG7C,aAAa,CAC3B+B,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACL,IAAI,CAAC,CACpBmB,GAAG,CAACd,CAAC,IAAI5C,SAAS,CAACC,GAAG,CAACC,EAAE,EAAE,qBAAqB,EAAE0C,CAAC,CAACT,EAAE,CAAC,EAAE;MAAEI,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE5E,MAAMoB,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC;IAC3BJ,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAMQ,mBAAmB,GAAIvB,IAAY,IAAK;IAC5C,IAAIA,IAAI,CAACwB,QAAQ,CAAC,MAAM,CAAC,EAAE;MACzB,oBAAOvD,OAAA,CAACnB,cAAc;QAAC2E,KAAK,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC3C,CAAC,MAAM,IAAI7B,IAAI,CAACwB,QAAQ,CAAC,UAAU,CAAC,EAAE;MACpC,oBAAOvD,OAAA,CAACvB,YAAY;QAAC+E,KAAK,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC3C,CAAC,MAAM,IAAI7B,IAAI,CAACwB,QAAQ,CAAC,aAAa,CAAC,EAAE;MACvC,oBAAOvD,OAAA,CAACrB,SAAS;QAAC6E,KAAK,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACtC,CAAC,MAAM;MACL,oBAAO5D,OAAA,CAACzB,eAAe;QAACiF,KAAK,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACzC;EACF,CAAC;EAED,MAAMC,UAAU,GAAIC,SAAc,IAAK;IACrC,IAAI,CAACA,SAAS,EAAE,OAAO,UAAU;IAEjC,IAAI;MACF,MAAMC,IAAI,GAAGD,SAAS,CAACE,MAAM,CAAC,CAAC;MAC/B,OAAOlE,mBAAmB,CAACiE,IAAI,EAAE;QAAEE,SAAS,EAAE;MAAK,CAAC,CAAC;IACvD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,OAAO,UAAU;IACnB;EACF,CAAC;EAED,oBACElE,OAAA,CAAAE,SAAA;IAAAiE,QAAA,gBACEnE,OAAA,CAAC5B,OAAO;MAACyD,KAAK,EAAC,kBAAkB;MAAAsC,QAAA,eAC/BnE,OAAA,CAACpC,UAAU;QACT4F,KAAK,EAAC,SAAS;QACfY,OAAO,EAAE7B,WAAY;QACrB8B,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,eAEdnE,OAAA,CAAC1C,KAAK;UAACiH,YAAY,EAAEhE,WAAY;UAACiD,KAAK,EAAC,OAAO;UAAAW,QAAA,eAC7CnE,OAAA,CAACzB,eAAe;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEV5D,OAAA,CAAC7B,OAAO;MACN8C,IAAI,EAAEA,IAAK;MACXR,QAAQ,EAAEA,QAAS;MACnB+D,OAAO,EAAE9B,WAAY;MACrB+B,YAAY,EAAE;QACZC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACd,CAAE;MACFC,eAAe,EAAE;QACfF,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACd,CAAE;MAAAR,QAAA,eAEFnE,OAAA,CAACvC,IAAI;QAAC4G,EAAE,EAAE;UAAEQ,KAAK,EAAE,GAAG;UAAEC,SAAS,EAAE;QAAI,CAAE;QAAAX,QAAA,gBACvCnE,OAAA,CAACzC,GAAG;UAAC8G,EAAE,EAAE;YAAEU,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAf,QAAA,gBACxFnE,OAAA,CAAC3B,UAAU;YAAC8G,OAAO,EAAC,IAAI;YAAAhB,QAAA,EAAC;UAAgB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACrDrD,WAAW,GAAG,CAAC,iBACdP,OAAA,CAACxC,MAAM;YAAC4H,IAAI,EAAC,OAAO;YAAChB,OAAO,EAAEnB,aAAc;YAAAkB,QAAA,EAAC;UAE7C;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN5D,OAAA,CAACrC,OAAO;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAEVvD,aAAa,CAACiC,MAAM,KAAK,CAAC,gBACzBtC,OAAA,CAACzC,GAAG;UAAC8G,EAAE,EAAE;YAAEU,CAAC,EAAE,CAAC;YAAEM,SAAS,EAAE;UAAS,CAAE;UAAAlB,QAAA,eACrCnE,OAAA,CAAC3B,UAAU;YAACmF,KAAK,EAAC,eAAe;YAAAW,QAAA,EAAC;UAAmB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,gBAEN5D,OAAA,CAACnC,IAAI;UAACwG,EAAE,EAAE;YAAES,SAAS,EAAE,GAAG;YAAEQ,QAAQ,EAAE;UAAO,CAAE;UAAAnB,QAAA,EAC5C9D,aAAa,CAAC8C,GAAG,CAAEP,YAAY,iBAC9B5C,OAAA,CAAClC,QAAQ;YAEPoH,UAAU,EAAC,YAAY;YACvBb,EAAE,EAAE;cACFkB,OAAO,EAAE3C,YAAY,CAACZ,IAAI,GAAG,aAAa,GAAG,cAAc;cAC3D,SAAS,EAAE;gBAAEuD,OAAO,EAAE;cAAkB,CAAC;cACzCC,MAAM,EAAE;YACV,CAAE;YACFpB,OAAO,EAAEA,CAAA,KAAMrB,uBAAuB,CAACH,YAAY,CAAE;YACrD6C,eAAe,eACbzF,OAAA,CAACpC,UAAU;cAAC8H,IAAI,EAAC,KAAK;cAACtB,OAAO,EAAGuB,CAAC,IAAKhD,eAAe,CAACgD,CAAC,EAAE/C,YAAY,CAAE;cAAAuB,QAAA,eACtEnE,OAAA,CAACjB,YAAY;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACb;YAAAO,QAAA,gBAEDnE,OAAA,CAACjC,cAAc;cAAAoG,QAAA,EACZb,mBAAmB,CAACV,YAAY,CAACf,KAAK,CAAC+D,WAAW,CAAC,CAAC;YAAC;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACjB5D,OAAA,CAAChC,YAAY;cACX6H,OAAO,EAAEjD,YAAY,CAACf,KAAM;cAC5BiE,SAAS,eACP9F,OAAA,CAAAE,SAAA;gBAAAiE,QAAA,gBACEnE,OAAA,CAAC3B,UAAU;kBACT0H,SAAS,EAAC,MAAM;kBAChBZ,OAAO,EAAC,OAAO;kBACf3B,KAAK,EAAC,aAAa;kBACnBa,EAAE,EAAE;oBAAEW,OAAO,EAAE;kBAAQ,CAAE;kBAAAb,QAAA,EAExBvB,YAAY,CAACd;gBAAO;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACb5D,OAAA,CAAC3B,UAAU;kBACT0H,SAAS,EAAC,MAAM;kBAChBZ,OAAO,EAAC,SAAS;kBACjB3B,KAAK,EAAC,eAAe;kBAAAW,QAAA,EAEpBN,UAAU,CAACjB,YAAY,CAACT,SAAS;gBAAC;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA,eACb;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA,GAtCGhB,YAAY,CAAChB,EAAE;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuCZ,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP,eAED5D,OAAA,CAACrC,OAAO;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACX5D,OAAA,CAACtC,WAAW;UAAAyG,QAAA,eACVnE,OAAA,CAACxC,MAAM;YACLwI,SAAS;YACTb,OAAO,EAAC,MAAM;YACdf,OAAO,EAAEA,CAAA,KAAM;cACbpD,QAAQ,CAAC,kBAAkB,CAAC;cAC5B0B,WAAW,CAAC,CAAC;YACf,CAAE;YAAAyB,QAAA,EACH;UAED;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEV5D,OAAA,CAAC/B,IAAI;MACHwC,QAAQ,EAAEE,YAAa;MACvBM,IAAI,EAAEE,QAAS;MACfqD,OAAO,EAAE1B,eAAgB;MAAAqB,QAAA,GAExBtD,oBAAoB,IAAI,CAACA,oBAAoB,CAACmB,IAAI,iBACjDhC,OAAA,CAAC9B,QAAQ;QAACkG,OAAO,EAAEA,CAAA,KAAMpB,UAAU,CAACnC,oBAAoB,CAAE;QAAAsD,QAAA,gBACxDnE,OAAA,CAACf,eAAe;UAACgH,QAAQ,EAAC,OAAO;UAAC5B,EAAE,EAAE;YAAE6B,EAAE,EAAE;UAAE;QAAE;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAErD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CACX,eACD5D,OAAA,CAAC9B,QAAQ;QAACkG,OAAO,EAAEtB,eAAgB;QAAAqB,QAAA,gBACjCnE,OAAA,CAACb,cAAc;UAAC8G,QAAQ,EAAC,OAAO;UAAC5B,EAAE,EAAE;YAAE6B,EAAE,EAAE;UAAE;QAAE;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,uBAEpD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA,eACP,CAAC;AAEP,CAAC;AAACxD,EAAA,CAnPID,0BAAoC;EAAA,QAMhBP,OAAO,EACdC,WAAW;AAAA;AAAAsG,EAAA,GAPxBhG,0BAAoC;AAqP1C,eAAeA,0BAA0B;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}