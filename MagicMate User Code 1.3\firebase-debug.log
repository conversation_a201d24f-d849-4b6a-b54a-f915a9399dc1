[debug] [2025-06-30T16:19:16.203Z] ----------------------------------------------------------------------
[debug] [2025-06-30T16:19:16.209Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js apps:create ios magicmate (ios) --bundle-id=magicmate --json --project=linkinblink-f544a
[debug] [2025-06-30T16:19:16.210Z] CLI Version:   14.5.1
[debug] [2025-06-30T16:19:16.211Z] Platform:      win32
[debug] [2025-06-30T16:19:16.211Z] Node Version:  v22.14.0
[debug] [2025-06-30T16:19:16.211Z] Time:          Mon Jun 30 2025 21:49:16 GMT+0530 (India Standard Time)
[debug] [2025-06-30T16:19:16.211Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-30T16:19:16.218Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-30T16:19:16.219Z] > authorizing via signed-in user (<EMAIL>)
[info] Create your IOS app in project linkinblink-f544a:
[debug] [2025-06-30T16:19:16.225Z] Checked if tokens are valid: true, expires at: 1751303741736
[debug] [2025-06-30T16:19:16.225Z] Checked if tokens are valid: true, expires at: 1751303741736
[debug] [2025-06-30T16:19:16.227Z] >>> [apiv2][query] POST https://firebase.googleapis.com/v1beta1/projects/linkinblink-f544a/iosApps [none]
[debug] [2025-06-30T16:19:16.227Z] >>> [apiv2][body] POST https://firebase.googleapis.com/v1beta1/projects/linkinblink-f544a/iosApps {"displayName":"magicmate (ios)","bundleId":"magicmate"}
[debug] [2025-06-30T16:19:16.286Z] Error: EPIPE: broken pipe, write
    at Socket._write (node:internal/net:63:18)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Ora.start (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\node_modules\ora\index.js:304:17)
    at initiateIosAppCreation (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\management\apps.js:77:50)
    at sdkInit (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\management\apps.js:137:29)
    at Command.actionFn (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\commands\apps-create.js:59:46)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\command.js:244:25
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
[error] 
[error] Error: An unexpected error has occurred.
