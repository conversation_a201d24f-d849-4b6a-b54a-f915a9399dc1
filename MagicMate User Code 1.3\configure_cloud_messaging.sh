#!/bin/bash

# Firebase Cloud Messaging Setup Script
# This script configures FCM for push notifications

echo "📱 Firebase Cloud Messaging Setup"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

PROJECT_ID="linkinblink-f544a"
PROJECT_NUMBER="408299360705"

echo -e "${BLUE}Setting up Cloud Messaging for project: $PROJECT_ID${NC}"
echo ""

# Step 1: Enable Cloud Messaging API
echo -e "${YELLOW}Step 1: Enabling Cloud Messaging API...${NC}"
gcloud services enable fcm.googleapis.com --project=$PROJECT_ID

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Cloud Messaging API enabled${NC}"
else
    echo -e "${RED}❌ Failed to enable Cloud Messaging API${NC}"
    exit 1
fi

# Step 2: Create FCM configuration
echo -e "${YELLOW}Step 2: Creating FCM configuration...${NC}"

cat > fcm_config.json << EOF
{
  "project_id": "$PROJECT_ID",
  "project_number": "$PROJECT_NUMBER",
  "api_key": "AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI",
  "messaging_sender_id": "$PROJECT_NUMBER",
  "app_id_web": "1:$PROJECT_NUMBER:web:a9e5c8f4c2b8e9a9e5c8f4",
  "app_id_android": "1:$PROJECT_NUMBER:android:a9e5c8f4c2b8e9a9e5c8f4",
  "app_id_ios": "1:$PROJECT_NUMBER:ios:a9e5c8f4c2b8e9a9e5c8f4"
}
EOF

echo -e "${GREEN}✅ Created FCM configuration${NC}"

# Step 3: Create notification service
echo -e "${YELLOW}Step 3: Creating notification service...${NC}"

cat > notification_service.js << 'EOF'
// Firebase Cloud Messaging Service
const admin = require('firebase-admin');

class NotificationService {
  
  constructor() {
    // Initialize Firebase Admin SDK if not already initialized
    if (!admin.apps.length) {
      admin.initializeApp();
    }
    this.messaging = admin.messaging();
  }

  // Send notification to single device
  async sendToDevice(token, title, body, data = {}) {
    const message = {
      notification: {
        title: title,
        body: body
      },
      data: data,
      token: token,
      android: {
        notification: {
          clickAction: 'FLUTTER_NOTIFICATION_CLICK',
          sound: 'default'
        }
      },
      apns: {
        payload: {
          aps: {
            sound: 'default'
          }
        }
      }
    };

    try {
      const response = await this.messaging.send(message);
      console.log('✅ Notification sent successfully:', response);
      return { success: true, messageId: response };
    } catch (error) {
      console.error('❌ Error sending notification:', error);
      return { success: false, error: error.message };
    }
  }

  // Send notification to multiple devices
  async sendToMultipleDevices(tokens, title, body, data = {}) {
    const message = {
      notification: {
        title: title,
        body: body
      },
      data: data,
      tokens: tokens
    };

    try {
      const response = await this.messaging.sendMulticast(message);
      console.log(`✅ Notifications sent: ${response.successCount}/${tokens.length}`);
      return { 
        success: true, 
        successCount: response.successCount,
        failureCount: response.failureCount,
        responses: response.responses 
      };
    } catch (error) {
      console.error('❌ Error sending notifications:', error);
      return { success: false, error: error.message };
    }
  }

  // Send notification to topic
  async sendToTopic(topic, title, body, data = {}) {
    const message = {
      notification: {
        title: title,
        body: body
      },
      data: data,
      topic: topic
    };

    try {
      const response = await this.messaging.send(message);
      console.log('✅ Topic notification sent:', response);
      return { success: true, messageId: response };
    } catch (error) {
      console.error('❌ Error sending topic notification:', error);
      return { success: false, error: error.message };
    }
  }

  // Subscribe device to topic
  async subscribeToTopic(tokens, topic) {
    try {
      const response = await this.messaging.subscribeToTopic(tokens, topic);
      console.log(`✅ Subscribed to topic ${topic}:`, response);
      return { success: true, response };
    } catch (error) {
      console.error('❌ Error subscribing to topic:', error);
      return { success: false, error: error.message };
    }
  }

  // Unsubscribe device from topic
  async unsubscribeFromTopic(tokens, topic) {
    try {
      const response = await this.messaging.unsubscribeFromTopic(tokens, topic);
      console.log(`✅ Unsubscribed from topic ${topic}:`, response);
      return { success: true, response };
    } catch (error) {
      console.error('❌ Error unsubscribing from topic:', error);
      return { success: false, error: error.message };
    }
  }

  // Send event booking confirmation
  async sendEventBookingConfirmation(token, eventTitle, bookingId) {
    return await this.sendToDevice(
      token,
      'Booking Confirmed! 🎉',
      `Your booking for "${eventTitle}" has been confirmed.`,
      {
        type: 'event_booking',
        booking_id: bookingId,
        click_action: 'FLUTTER_NOTIFICATION_CLICK'
      }
    );
  }

  // Send hotel booking confirmation
  async sendHotelBookingConfirmation(token, hotelName, bookingId) {
    return await this.sendToDevice(
      token,
      'Hotel Booking Confirmed! 🏨',
      `Your booking at "${hotelName}" has been confirmed.`,
      {
        type: 'hotel_booking',
        booking_id: bookingId,
        click_action: 'FLUTTER_NOTIFICATION_CLICK'
      }
    );
  }

  // Send chat message notification
  async sendChatNotification(token, senderName, message) {
    return await this.sendToDevice(
      token,
      `New message from ${senderName} 💬`,
      message,
      {
        type: 'chat_message',
        sender_name: senderName,
        click_action: 'FLUTTER_NOTIFICATION_CLICK'
      }
    );
  }

  // Send payment confirmation
  async sendPaymentConfirmation(token, amount, transactionId) {
    return await this.sendToDevice(
      token,
      'Payment Successful! 💳',
      `Your payment of ₹${amount} has been processed successfully.`,
      {
        type: 'payment_confirmation',
        amount: amount.toString(),
        transaction_id: transactionId,
        click_action: 'FLUTTER_NOTIFICATION_CLICK'
      }
    );
  }

  // Send general announcement
  async sendAnnouncement(topic, title, body) {
    return await this.sendToTopic(
      topic,
      title,
      body,
      {
        type: 'announcement',
        click_action: 'FLUTTER_NOTIFICATION_CLICK'
      }
    );
  }
}

module.exports = NotificationService;
EOF

echo -e "${GREEN}✅ Created notification service${NC}"

# Step 4: Create test notification script
echo -e "${YELLOW}Step 4: Creating test notification script...${NC}"

cat > test_notifications.js << 'EOF'
// Test Notifications Script
const NotificationService = require('./notification_service');

async function testNotifications() {
  console.log('📱 Testing Firebase Cloud Messaging...');
  
  const notificationService = new NotificationService();
  
  // Test token (replace with actual device token)
  const testToken = 'TEST_FCM_TOKEN_HERE';
  
  try {
    // Test 1: Simple notification
    console.log('🧪 Test 1: Simple notification...');
    const result1 = await notificationService.sendToDevice(
      testToken,
      'Test Notification',
      'This is a test notification from MagicMate!'
    );
    console.log('Result:', result1);

    // Test 2: Event booking notification
    console.log('🧪 Test 2: Event booking notification...');
    const result2 = await notificationService.sendEventBookingConfirmation(
      testToken,
      'Music Concert 2024',
      'booking_123'
    );
    console.log('Result:', result2);

    // Test 3: Topic notification
    console.log('🧪 Test 3: Topic notification...');
    const result3 = await notificationService.sendAnnouncement(
      'all_users',
      'New Feature Available! 🚀',
      'Check out the latest updates in MagicMate app!'
    );
    console.log('Result:', result3);

    console.log('');
    console.log('✅ All notification tests completed!');
    console.log('');
    console.log('📝 Note: Replace TEST_FCM_TOKEN_HERE with actual device tokens');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run tests
if (require.main === module) {
  testNotifications().then(() => {
    console.log('🎉 Notification testing complete!');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Notification testing failed:', error);
    process.exit(1);
  });
}

module.exports = { testNotifications };
EOF

echo -e "${GREEN}✅ Created test notification script${NC}"

# Step 5: Create topic management script
echo -e "${YELLOW}Step 5: Creating topic management script...${NC}"

cat > manage_topics.js << 'EOF'
// Topic Management Script
const NotificationService = require('./notification_service');

async function setupTopics() {
  console.log('📢 Setting up FCM topics...');
  
  const notificationService = new NotificationService();
  
  // Define topics
  const topics = [
    'all_users',
    'event_users', 
    'hotel_users',
    'organizers',
    'premium_users',
    'announcements'
  ];

  console.log('📋 Topics to create:');
  topics.forEach(topic => {
    console.log(`  - ${topic}`);
  });

  console.log('');
  console.log('✅ Topics are created automatically when first used');
  console.log('');
  console.log('📱 To subscribe devices to topics, use:');
  console.log('notificationService.subscribeToTopic([token], "topic_name")');
  console.log('');
  console.log('📢 To send to topics, use:');
  console.log('notificationService.sendToTopic("topic_name", "title", "body")');

  return topics;
}

// Test topic subscription
async function testTopicSubscription() {
  console.log('🧪 Testing topic subscription...');
  
  const notificationService = new NotificationService();
  const testToken = 'TEST_FCM_TOKEN_HERE';
  
  try {
    // Subscribe to all_users topic
    const result = await notificationService.subscribeToTopic([testToken], 'all_users');
    console.log('Subscription result:', result);
    
    // Send test message to topic
    const sendResult = await notificationService.sendToTopic(
      'all_users',
      'Welcome to MagicMate! 🎉',
      'You are now subscribed to receive important updates.'
    );
    console.log('Send result:', sendResult);
    
  } catch (error) {
    console.error('❌ Topic test failed:', error);
  }
}

// Run if called directly
if (require.main === module) {
  setupTopics().then(() => {
    console.log('✅ Topic setup complete!');
    return testTopicSubscription();
  }).then(() => {
    console.log('✅ Topic testing complete!');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Topic setup failed:', error);
    process.exit(1);
  });
}

module.exports = { setupTopics, testTopicSubscription };
EOF

echo -e "${GREEN}✅ Created topic management script${NC}"

# Step 6: Update package.json with FCM scripts
echo -e "${YELLOW}Step 6: Updating package.json with FCM scripts...${NC}"

# Add FCM scripts to package.json
if [ -f package.json ]; then
    # Create backup
    cp package.json package.json.backup
    
    # Update package.json with new scripts
    cat > package.json << 'EOF'
{
  "name": "magicmate-firebase-setup",
  "version": "1.0.0",
  "description": "Firebase setup for MagicMate",
  "main": "index.js",
  "scripts": {
    "create-collections": "node create_firestore_collections.js",
    "create-test-users": "node create_test_users.js",
    "test-auth": "node test_auth.js",
    "test-notifications": "node test_notifications.js",
    "setup-topics": "node manage_topics.js",
    "test-fcm": "npm run test-notifications && npm run setup-topics"
  },
  "dependencies": {
    "firebase-admin": "^12.0.0"
  }
}
EOF
    echo -e "${GREEN}✅ Updated package.json with FCM scripts${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Firebase Cloud Messaging Setup Complete!${NC}"
echo ""
echo -e "${BLUE}Next Steps:${NC}"
echo "1. Get FCM Server Key from Firebase Console"
echo "2. Update test scripts with actual device tokens"
echo "3. Run: npm run test-notifications"
echo "4. Run: npm run setup-topics"
echo ""
echo -e "${YELLOW}Firebase Console URLs:${NC}"
echo "Cloud Messaging: https://console.firebase.google.com/project/$PROJECT_ID/settings/cloudmessaging"
echo "Project Settings: https://console.firebase.google.com/project/$PROJECT_ID/settings/general"
echo ""
echo -e "${BLUE}Available Scripts:${NC}"
echo "npm run test-notifications  - Test notification sending"
echo "npm run setup-topics        - Setup and test topics"
echo "npm run test-fcm            - Run all FCM tests"
echo ""
echo -e "${BLUE}FCM Configuration:${NC}"
echo "Project ID: $PROJECT_ID"
echo "Project Number: $PROJECT_NUMBER"
echo "Sender ID: $PROJECT_NUMBER"
