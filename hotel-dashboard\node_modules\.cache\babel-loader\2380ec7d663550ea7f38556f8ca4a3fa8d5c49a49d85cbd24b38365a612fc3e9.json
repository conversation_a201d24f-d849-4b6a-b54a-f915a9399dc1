{"ast": null, "code": "import { createUnarySpacing } from '../spacing';\n\n// The different signatures imply different meaning for their arguments that can't be expressed structurally.\n// We express the difference with variable names.\n\nexport default function createSpacing() {\n  let spacingInput = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 8;\n  // Already transformed.\n  if (spacingInput.mui) {\n    return spacingInput;\n  }\n\n  // Material Design layouts are visually balanced. Most measurements align to an 8dp grid, which aligns both spacing and the overall layout.\n  // Smaller components, such as icons, can align to a 4dp grid.\n  // https://m2.material.io/design/layout/understanding-layout.html\n  const transform = createUnarySpacing({\n    spacing: spacingInput\n  });\n  const spacing = function () {\n    for (var _len = arguments.length, argsInput = new Array(_len), _key = 0; _key < _len; _key++) {\n      argsInput[_key] = arguments[_key];\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (!(argsInput.length <= 4)) {\n        console.error(\"MUI: Too many arguments provided, expected between 0 and 4, got \".concat(argsInput.length));\n      }\n    }\n    const args = argsInput.length === 0 ? [1] : argsInput;\n    return args.map(argument => {\n      const output = transform(argument);\n      return typeof output === 'number' ? \"\".concat(output, \"px\") : output;\n    }).join(' ');\n  };\n  spacing.mui = true;\n  return spacing;\n}", "map": {"version": 3, "names": ["createUnarySpacing", "createSpacing", "spacingInput", "arguments", "length", "undefined", "mui", "transform", "spacing", "_len", "argsInput", "Array", "_key", "process", "env", "NODE_ENV", "console", "error", "concat", "args", "map", "argument", "output", "join"], "sources": ["G:/linkinblink/hotel-dashboard/node_modules/@mui/system/esm/createTheme/createSpacing.js"], "sourcesContent": ["import { createUnarySpacing } from '../spacing';\n\n// The different signatures imply different meaning for their arguments that can't be expressed structurally.\n// We express the difference with variable names.\n\nexport default function createSpacing(spacingInput = 8) {\n  // Already transformed.\n  if (spacingInput.mui) {\n    return spacingInput;\n  }\n\n  // Material Design layouts are visually balanced. Most measurements align to an 8dp grid, which aligns both spacing and the overall layout.\n  // Smaller components, such as icons, can align to a 4dp grid.\n  // https://m2.material.io/design/layout/understanding-layout.html\n  const transform = createUnarySpacing({\n    spacing: spacingInput\n  });\n  const spacing = (...argsInput) => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!(argsInput.length <= 4)) {\n        console.error(`MUI: Too many arguments provided, expected between 0 and 4, got ${argsInput.length}`);\n      }\n    }\n    const args = argsInput.length === 0 ? [1] : argsInput;\n    return args.map(argument => {\n      const output = transform(argument);\n      return typeof output === 'number' ? `${output}px` : output;\n    }).join(' ');\n  };\n  spacing.mui = true;\n  return spacing;\n}"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,YAAY;;AAE/C;AACA;;AAEA,eAAe,SAASC,aAAaA,CAAA,EAAmB;EAAA,IAAlBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACpD;EACA,IAAID,YAAY,CAACI,GAAG,EAAE;IACpB,OAAOJ,YAAY;EACrB;;EAEA;EACA;EACA;EACA,MAAMK,SAAS,GAAGP,kBAAkB,CAAC;IACnCQ,OAAO,EAAEN;EACX,CAAC,CAAC;EACF,MAAMM,OAAO,GAAG,SAAAA,CAAA,EAAkB;IAAA,SAAAC,IAAA,GAAAN,SAAA,CAAAC,MAAA,EAAdM,SAAS,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAATF,SAAS,CAAAE,IAAA,IAAAT,SAAA,CAAAS,IAAA;IAAA;IAC3B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,EAAEL,SAAS,CAACN,MAAM,IAAI,CAAC,CAAC,EAAE;QAC5BY,OAAO,CAACC,KAAK,oEAAAC,MAAA,CAAoER,SAAS,CAACN,MAAM,CAAE,CAAC;MACtG;IACF;IACA,MAAMe,IAAI,GAAGT,SAAS,CAACN,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGM,SAAS;IACrD,OAAOS,IAAI,CAACC,GAAG,CAACC,QAAQ,IAAI;MAC1B,MAAMC,MAAM,GAAGf,SAAS,CAACc,QAAQ,CAAC;MAClC,OAAO,OAAOC,MAAM,KAAK,QAAQ,MAAAJ,MAAA,CAAMI,MAAM,UAAOA,MAAM;IAC5D,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACd,CAAC;EACDf,OAAO,CAACF,GAAG,GAAG,IAAI;EAClB,OAAOE,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}