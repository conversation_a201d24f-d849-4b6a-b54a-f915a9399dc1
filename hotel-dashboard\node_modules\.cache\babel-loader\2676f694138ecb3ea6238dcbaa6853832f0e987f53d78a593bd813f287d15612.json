{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization';\nconst views = {\n  hours: '小时',\n  minutes: '分钟',\n  seconds: '秒'\n};\nconst zhCNPickers = {\n  // Calendar navigation\n  previousMonth: '上个月',\n  nextMonth: '下个月',\n  // View navigation\n  openPreviousView: '前一个视图',\n  openNextView: '下一个视图',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? '年视图已打开，切换为日历视图' : '日历视图已打开，切换为年视图',\n  // inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,\n  // DateRange placeholders\n  start: '开始',\n  end: '结束',\n  // Action bar\n  cancelButtonLabel: '取消',\n  clearButtonLabel: '清除',\n  okButtonLabel: '确认',\n  todayButtonLabel: '今天',\n  // Toolbar titles\n  // datePickerDefaultToolbarTitle: 'Select date',\n  // dateTimePickerDefaultToolbarTitle: 'Select date & time',\n  // timePickerDefaultToolbarTitle: 'Select time',\n  // dateRangePickerDefaultToolbarTitle: 'Select date range',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => `Select ${views[view]}. ${time === null ? '未选择时间' : `已选择${adapter.format(time, 'fullTime')}`}`,\n  hoursClockNumberText: hours => `${hours}小时`,\n  minutesClockNumberText: minutes => `${minutes}分钟`,\n  secondsClockNumberText: seconds => `${seconds}秒`,\n  // Open picker labels\n  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `选择日期，已选择${utils.format(utils.date(rawValue), 'fullDate')}` : '选择日期',\n  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `选择时间，已选择${utils.format(utils.date(rawValue), 'fullTime')}` : '选择时间',\n  // Table labels\n  timeTableLabel: '选择时间',\n  dateTableLabel: '选择日期'\n};\nexport const zhCN = getPickersLocalization(zhCNPickers);", "map": {"version": 3, "names": ["getPickersLocalization", "views", "hours", "minutes", "seconds", "zhCNPickers", "previousMonth", "nextMonth", "openPreviousView", "openNextView", "calendarViewSwitchingButtonAriaLabel", "view", "start", "end", "cancelButtonLabel", "clearButtonLabel", "okButtonLabel", "todayButtonLabel", "clockLabelText", "time", "adapter", "format", "hoursClockNumberText", "minutesClockNumberText", "secondsClockNumberText", "openDatePickerDialogue", "rawValue", "utils", "<PERSON><PERSON><PERSON><PERSON>", "date", "openTimePickerDialogue", "timeTable<PERSON>abel", "dateTableLabel", "zhCN"], "sources": ["G:/linkinblink/hotel-dashboard/node_modules/@mui/x-date-pickers/locales/zhCN.js"], "sourcesContent": ["import { getPickersLocalization } from './utils/getPickersLocalization';\nconst views = {\n  hours: '小时',\n  minutes: '分钟',\n  seconds: '秒'\n};\nconst zhCNPickers = {\n  // Calendar navigation\n  previousMonth: '上个月',\n  nextMonth: '下个月',\n  // View navigation\n  openPreviousView: '前一个视图',\n  openNextView: '下一个视图',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? '年视图已打开，切换为日历视图' : '日历视图已打开，切换为年视图',\n  // inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,\n  // DateRange placeholders\n  start: '开始',\n  end: '结束',\n  // Action bar\n  cancelButtonLabel: '取消',\n  clearButtonLabel: '清除',\n  okButtonLabel: '确认',\n  todayButtonLabel: '今天',\n  // Toolbar titles\n  // datePickerDefaultToolbarTitle: 'Select date',\n  // dateTimePickerDefaultToolbarTitle: 'Select date & time',\n  // timePickerDefaultToolbarTitle: 'Select time',\n  // dateRangePickerDefaultToolbarTitle: 'Select date range',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => `Select ${views[view]}. ${time === null ? '未选择时间' : `已选择${adapter.format(time, 'fullTime')}`}`,\n  hoursClockNumberText: hours => `${hours}小时`,\n  minutesClockNumberText: minutes => `${minutes}分钟`,\n  secondsClockNumberText: seconds => `${seconds}秒`,\n  // Open picker labels\n  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `选择日期，已选择${utils.format(utils.date(rawValue), 'fullDate')}` : '选择日期',\n  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `选择时间，已选择${utils.format(utils.date(rawValue), 'fullTime')}` : '选择时间',\n  // Table labels\n  timeTableLabel: '选择时间',\n  dateTableLabel: '选择日期'\n};\nexport const zhCN = getPickersLocalization(zhCNPickers);"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,gCAAgC;AACvE,MAAMC,KAAK,GAAG;EACZC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACX,CAAC;AACD,MAAMC,WAAW,GAAG;EAClB;EACAC,aAAa,EAAE,KAAK;EACpBC,SAAS,EAAE,KAAK;EAChB;EACAC,gBAAgB,EAAE,OAAO;EACzBC,YAAY,EAAE,OAAO;EACrBC,oCAAoC,EAAEC,IAAI,IAAIA,IAAI,KAAK,MAAM,GAAG,gBAAgB,GAAG,gBAAgB;EACnG;EACA;EACAC,KAAK,EAAE,IAAI;EACXC,GAAG,EAAE,IAAI;EACT;EACAC,iBAAiB,EAAE,IAAI;EACvBC,gBAAgB,EAAE,IAAI;EACtBC,aAAa,EAAE,IAAI;EACnBC,gBAAgB,EAAE,IAAI;EACtB;EACA;EACA;EACA;EACA;EACA;EACAC,cAAc,EAAEA,CAACP,IAAI,EAAEQ,IAAI,EAAEC,OAAO,KAAK,UAAUnB,KAAK,CAACU,IAAI,CAAC,KAAKQ,IAAI,KAAK,IAAI,GAAG,OAAO,GAAG,MAAMC,OAAO,CAACC,MAAM,CAACF,IAAI,EAAE,UAAU,CAAC,EAAE,EAAE;EACvIG,oBAAoB,EAAEpB,KAAK,IAAI,GAAGA,KAAK,IAAI;EAC3CqB,sBAAsB,EAAEpB,OAAO,IAAI,GAAGA,OAAO,IAAI;EACjDqB,sBAAsB,EAAEpB,OAAO,IAAI,GAAGA,OAAO,GAAG;EAChD;EACAqB,sBAAsB,EAAEA,CAACC,QAAQ,EAAEC,KAAK,KAAKD,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,IAAI,CAACH,QAAQ,CAAC,CAAC,GAAG,WAAWC,KAAK,CAACN,MAAM,CAACM,KAAK,CAACE,IAAI,CAACH,QAAQ,CAAC,EAAE,UAAU,CAAC,EAAE,GAAG,MAAM;EACnKI,sBAAsB,EAAEA,CAACJ,QAAQ,EAAEC,KAAK,KAAKD,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,IAAI,CAACH,QAAQ,CAAC,CAAC,GAAG,WAAWC,KAAK,CAACN,MAAM,CAACM,KAAK,CAACE,IAAI,CAACH,QAAQ,CAAC,EAAE,UAAU,CAAC,EAAE,GAAG,MAAM;EACnK;EACAK,cAAc,EAAE,MAAM;EACtBC,cAAc,EAAE;AAClB,CAAC;AACD,OAAO,MAAMC,IAAI,GAAGjC,sBAAsB,CAACK,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}