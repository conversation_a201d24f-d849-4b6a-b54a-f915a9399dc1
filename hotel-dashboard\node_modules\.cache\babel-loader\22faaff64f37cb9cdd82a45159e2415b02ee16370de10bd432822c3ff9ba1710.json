{"ast": null, "code": "var _jsxFileName = \"G:\\\\linkinblink\\\\hotel-dashboard\\\\src\\\\pages\\\\staff\\\\CheckIn.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Paper, Grid, TextField, Button, Divider, Alert, CircularProgress, Chip, IconButton } from '@mui/material';\nimport { Search as SearchIcon, CheckCircle as CheckCircleIcon, Person as PersonIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { getBookingByNumber } from '../../services/bookingService';\nimport { getVerificationByUserId } from '../../services/verificationService';\nimport AadhaarVerificationCard from '../../components/AadhaarVerificationCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CheckIn = () => {\n  _s();\n  const [bookingNumber, setBookingNumber] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [booking, setBooking] = useState(null);\n  const [verification, setVerification] = useState(null);\n  const [verificationLoading, setVerificationLoading] = useState(false);\n  const [verificationError, setVerificationError] = useState(null);\n  const [checkInSuccess, setCheckInSuccess] = useState(false);\n\n  // Handle search booking\n  const handleSearchBooking = async () => {\n    if (!bookingNumber.trim()) {\n      setError('Please enter a booking number');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    setBooking(null);\n    setVerification(null);\n    setCheckInSuccess(false);\n    try {\n      const bookingData = await getBookingByNumber(bookingNumber.trim());\n      setBooking(bookingData);\n      if (bookingData && bookingData.userId) {\n        // Fetch verification data if booking is found\n        await fetchVerificationData(bookingData.userId);\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to find booking');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch verification data for a user\n  const fetchVerificationData = async userId => {\n    setVerificationLoading(true);\n    setVerificationError(null);\n    try {\n      const verificationData = await getVerificationByUserId(userId);\n      setVerification(verificationData);\n    } catch (err) {\n      setVerificationError(err.message || 'Failed to fetch verification data');\n    } finally {\n      setVerificationLoading(false);\n    }\n  };\n\n  // Handle check-in\n  const handleCheckIn = async () => {\n    // In a real app, you would update the booking status in Firestore\n    // For this demo, we'll just show a success message\n    setCheckInSuccess(true);\n  };\n\n  // Format date\n  const formatDate = date => {\n    if (!date) return 'N/A';\n    const dateObj = date.toDate ? date.toDate() : new Date(date);\n    return dateObj.toLocaleDateString('en-US', {\n      weekday: 'short',\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Guest Check-In\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Booking Number\",\n            variant: \"outlined\",\n            value: bookingNumber,\n            onChange: e => setBookingNumber(e.target.value),\n            placeholder: \"Enter booking number\",\n            InputProps: {\n              endAdornment: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: handleSearchBooking,\n                disabled: loading,\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this)\n            },\n            onKeyPress: e => {\n              if (e.key === 'Enter') {\n                handleSearchBooking();\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleSearchBooking,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 69\n            }, this),\n            children: \"Search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mt: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), booking && /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Booking Details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                  sx: {\n                    mr: 1,\n                    color: 'primary.main'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  children: booking.guestName || 'Guest'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Booking Number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                gutterBottom: true,\n                children: booking.bookingNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: booking.status || 'Confirmed',\n                color: booking.status === 'checked_in' ? 'success' : 'primary',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Check-In Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                gutterBottom: true,\n                children: formatDate(booking.checkInDate)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Check-Out Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                gutterBottom: true,\n                children: formatDate(booking.checkOutDate)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Room\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                gutterBottom: true,\n                children: booking.roomName || 'Standard Room'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Special Requests\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                gutterBottom: true,\n                children: booking.specialRequests || 'None'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            mt: 3,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              fullWidth: true,\n              onClick: handleCheckIn,\n              disabled: booking.status === 'checked_in' || !verification || verification.status !== 'verified',\n              startIcon: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 30\n              }, this),\n              children: \"Complete Check-In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this), booking.status === 'checked_in' && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mt: 2\n              },\n              children: \"This guest has already checked in.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 19\n            }, this), (!verification || verification.status !== 'verified') && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mt: 2\n              },\n              children: \"Aadhaar verification is required for check-in.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 19\n            }, this), checkInSuccess && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"success\",\n              sx: {\n                mt: 2\n              },\n              children: \"Check-in completed successfully!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: [/*#__PURE__*/_jsxDEV(AadhaarVerificationCard, {\n          verification: verification,\n          loading: verificationLoading,\n          error: verificationError || undefined,\n          onRefresh: () => (booking === null || booking === void 0 ? void 0 : booking.userId) && fetchVerificationData(booking.userId)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this), verification && verification.status !== 'verified' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            mb: 3,\n            bgcolor: 'warning.light'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            mb: 2,\n            children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n              color: \"warning\",\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Verification Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: \"The guest needs to complete Aadhaar verification through the mobile app before check-in.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Please ask the guest to open the hotel booking app and complete verification from their profile section.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 9\n    }, this), !booking && !loading && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Check-In Instructions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        paragraph: true,\n        children: \"1. Enter the booking number provided by the guest\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        paragraph: true,\n        children: \"2. Verify the guest's identity using their Aadhaar verification\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        paragraph: true,\n        children: \"3. Complete the check-in process\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Guests must complete Aadhaar verification through the mobile app before check-in.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n};\n_s(CheckIn, \"za+w3RMX5PGILI4GF6LY/7JMb2w=\");\n_c = CheckIn;\nexport default CheckIn;\nvar _c;\n$RefreshReg$(_c, \"CheckIn\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "Grid", "TextField", "<PERSON><PERSON>", "Divider", "<PERSON><PERSON>", "CircularProgress", "Chip", "IconButton", "Search", "SearchIcon", "CheckCircle", "CheckCircleIcon", "Person", "PersonIcon", "Warning", "WarningIcon", "getBookingByNumber", "getVerificationByUserId", "AadhaarVerificationCard", "jsxDEV", "_jsxDEV", "CheckIn", "_s", "bookingNumber", "setBookingNumber", "loading", "setLoading", "error", "setError", "booking", "setBooking", "verification", "setVerification", "verificationLoading", "setVerificationLoading", "verificationError", "setVerificationError", "checkInSuccess", "setCheckInSuccess", "handleSearchBooking", "trim", "bookingData", "userId", "fetchVerificationData", "err", "message", "verificationData", "handleCheckIn", "formatDate", "date", "date<PERSON><PERSON>j", "toDate", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "container", "spacing", "alignItems", "item", "xs", "sm", "fullWidth", "label", "value", "onChange", "e", "target", "placeholder", "InputProps", "endAdornment", "onClick", "disabled", "size", "onKeyPress", "key", "startIcon", "severity", "mt", "md", "display", "mr", "color", "<PERSON><PERSON><PERSON>", "status", "checkInDate", "checkOutDate", "roomName", "specialRequests", "undefined", "onRefresh", "bgcolor", "paragraph", "_c", "$RefreshReg$"], "sources": ["G:/linkinblink/hotel-dashboard/src/pages/staff/CheckIn.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  TextField,\n  Button,\n  Divider,\n  Card,\n  CardContent,\n  Alert,\n  CircularProgress,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemAvatar,\n  Avatar,\n  Tooltip\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  CheckCircle as CheckCircleIcon,\n  Cancel as CancelIcon,\n  Person as PersonIcon,\n  CalendarToday as CalendarIcon,\n  Hotel as HotelIcon,\n  VerifiedUser as VerifiedUserIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport { getBookingByNumber } from '../../services/bookingService';\nimport { getVerificationByUserId } from '../../services/verificationService';\nimport AadhaarVerificationCard from '../../components/AadhaarVerificationCard';\n\nconst CheckIn: React.FC = () => {\n  const [bookingNumber, setBookingNumber] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [booking, setBooking] = useState<any | null>(null);\n  const [verification, setVerification] = useState<any | null>(null);\n  const [verificationLoading, setVerificationLoading] = useState(false);\n  const [verificationError, setVerificationError] = useState<string | null>(null);\n  const [checkInSuccess, setCheckInSuccess] = useState(false);\n\n  // Handle search booking\n  const handleSearchBooking = async () => {\n    if (!bookingNumber.trim()) {\n      setError('Please enter a booking number');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n    setBooking(null);\n    setVerification(null);\n    setCheckInSuccess(false);\n\n    try {\n      const bookingData = await getBookingByNumber(bookingNumber.trim());\n      setBooking(bookingData);\n\n      if (bookingData && bookingData.userId) {\n        // Fetch verification data if booking is found\n        await fetchVerificationData(bookingData.userId);\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to find booking');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch verification data for a user\n  const fetchVerificationData = async (userId: string) => {\n    setVerificationLoading(true);\n    setVerificationError(null);\n\n    try {\n      const verificationData = await getVerificationByUserId(userId);\n      setVerification(verificationData);\n    } catch (err: any) {\n      setVerificationError(err.message || 'Failed to fetch verification data');\n    } finally {\n      setVerificationLoading(false);\n    }\n  };\n\n  // Handle check-in\n  const handleCheckIn = async () => {\n    // In a real app, you would update the booking status in Firestore\n    // For this demo, we'll just show a success message\n    setCheckInSuccess(true);\n  };\n\n  // Format date\n  const formatDate = (date: any) => {\n    if (!date) return 'N/A';\n    const dateObj = date.toDate ? date.toDate() : new Date(date);\n    return dateObj.toLocaleDateString('en-US', {\n      weekday: 'short',\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        Guest Check-In\n      </Typography>\n\n      {/* Search Form */}\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Grid container spacing={2} alignItems=\"center\">\n          <Grid item xs={12} sm={6}>\n            <TextField\n              fullWidth\n              label=\"Booking Number\"\n              variant=\"outlined\"\n              value={bookingNumber}\n              onChange={(e) => setBookingNumber(e.target.value)}\n              placeholder=\"Enter booking number\"\n              InputProps={{\n                endAdornment: (\n                  <IconButton\n                    onClick={handleSearchBooking}\n                    disabled={loading}\n                    size=\"small\"\n                  >\n                    <SearchIcon />\n                  </IconButton>\n                ),\n              }}\n              onKeyPress={(e) => {\n                if (e.key === 'Enter') {\n                  handleSearchBooking();\n                }\n              }}\n            />\n          </Grid>\n          <Grid item xs={12} sm={6}>\n            <Button\n              variant=\"contained\"\n              onClick={handleSearchBooking}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} /> : <SearchIcon />}\n            >\n              Search\n            </Button>\n          </Grid>\n        </Grid>\n\n        {error && (\n          <Alert severity=\"error\" sx={{ mt: 2 }}>\n            {error}\n          </Alert>\n        )}\n      </Paper>\n\n      {/* Booking Details */}\n      {booking && (\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={6}>\n            <Paper sx={{ p: 3, mb: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Booking Details\n              </Typography>\n              <Divider sx={{ mb: 2 }} />\n\n              <Grid container spacing={2}>\n                <Grid item xs={12}>\n                  <Box display=\"flex\" alignItems=\"center\">\n                    <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />\n                    <Typography variant=\"subtitle1\">\n                      {booking.guestName || 'Guest'}\n                    </Typography>\n                  </Box>\n                </Grid>\n\n                <Grid item xs={12} sm={6}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Booking Number\n                  </Typography>\n                  <Typography variant=\"body1\" gutterBottom>\n                    {booking.bookingNumber}\n                  </Typography>\n                </Grid>\n\n                <Grid item xs={12} sm={6}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Status\n                  </Typography>\n                  <Chip\n                    label={booking.status || 'Confirmed'}\n                    color={booking.status === 'checked_in' ? 'success' : 'primary'}\n                    size=\"small\"\n                  />\n                </Grid>\n\n                <Grid item xs={12} sm={6}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Check-In Date\n                  </Typography>\n                  <Typography variant=\"body1\" gutterBottom>\n                    {formatDate(booking.checkInDate)}\n                  </Typography>\n                </Grid>\n\n                <Grid item xs={12} sm={6}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Check-Out Date\n                  </Typography>\n                  <Typography variant=\"body1\" gutterBottom>\n                    {formatDate(booking.checkOutDate)}\n                  </Typography>\n                </Grid>\n\n                <Grid item xs={12}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Room\n                  </Typography>\n                  <Typography variant=\"body1\" gutterBottom>\n                    {booking.roomName || 'Standard Room'}\n                  </Typography>\n                </Grid>\n\n                <Grid item xs={12}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Special Requests\n                  </Typography>\n                  <Typography variant=\"body1\" gutterBottom>\n                    {booking.specialRequests || 'None'}\n                  </Typography>\n                </Grid>\n              </Grid>\n\n              <Box mt={3}>\n                <Button\n                  variant=\"contained\"\n                  color=\"primary\"\n                  fullWidth\n                  onClick={handleCheckIn}\n                  disabled={booking.status === 'checked_in' || !verification || verification.status !== 'verified'}\n                  startIcon={<CheckCircleIcon />}\n                >\n                  Complete Check-In\n                </Button>\n                {booking.status === 'checked_in' && (\n                  <Alert severity=\"info\" sx={{ mt: 2 }}>\n                    This guest has already checked in.\n                  </Alert>\n                )}\n                {(!verification || verification.status !== 'verified') && (\n                  <Alert severity=\"warning\" sx={{ mt: 2 }}>\n                    Aadhaar verification is required for check-in.\n                  </Alert>\n                )}\n                {checkInSuccess && (\n                  <Alert severity=\"success\" sx={{ mt: 2 }}>\n                    Check-in completed successfully!\n                  </Alert>\n                )}\n              </Box>\n            </Paper>\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            {/* Aadhaar Verification Card */}\n            <AadhaarVerificationCard\n              verification={verification}\n              loading={verificationLoading}\n              error={verificationError || undefined}\n              onRefresh={() => booking?.userId && fetchVerificationData(booking.userId)}\n            />\n\n            {/* Verification Status */}\n            {verification && verification.status !== 'verified' && (\n              <Paper sx={{ p: 3, mb: 3, bgcolor: 'warning.light' }}>\n                <Box display=\"flex\" alignItems=\"center\" mb={2}>\n                  <WarningIcon color=\"warning\" sx={{ mr: 1 }} />\n                  <Typography variant=\"h6\">Verification Required</Typography>\n                </Box>\n                <Typography variant=\"body1\" paragraph>\n                  The guest needs to complete Aadhaar verification through the mobile app before check-in.\n                </Typography>\n                <Typography variant=\"body2\">\n                  Please ask the guest to open the hotel booking app and complete verification from their profile section.\n                </Typography>\n              </Paper>\n            )}\n          </Grid>\n        </Grid>\n      )}\n\n      {/* Instructions when no booking is loaded */}\n      {!booking && !loading && (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Check-In Instructions\n          </Typography>\n          <Typography variant=\"body1\" paragraph>\n            1. Enter the booking number provided by the guest\n          </Typography>\n          <Typography variant=\"body1\" paragraph>\n            2. Verify the guest's identity using their Aadhaar verification\n          </Typography>\n          <Typography variant=\"body1\" paragraph>\n            3. Complete the check-in process\n          </Typography>\n          <Alert severity=\"info\">\n            Guests must complete Aadhaar verification through the mobile app before check-in.\n          </Alert>\n        </Paper>\n      )}\n    </Box>\n  );\n};\n\nexport default CheckIn;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,SAAS,EACTC,MAAM,EACNC,OAAO,EAGPC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,UAAU,QAWL,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAE9BC,MAAM,IAAIC,UAAU,EAIpBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,uBAAuB,QAAQ,oCAAoC;AAC5E,OAAOC,uBAAuB,MAAM,0CAA0C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/E,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAa,IAAI,CAAC;EACxD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAa,IAAI,CAAC;EAClE,MAAM,CAACqC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACuC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxC,QAAQ,CAAgB,IAAI,CAAC;EAC/E,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM2C,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAAChB,aAAa,CAACiB,IAAI,CAAC,CAAC,EAAE;MACzBZ,QAAQ,CAAC,+BAA+B,CAAC;MACzC;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChBE,eAAe,CAAC,IAAI,CAAC;IACrBM,iBAAiB,CAAC,KAAK,CAAC;IAExB,IAAI;MACF,MAAMG,WAAW,GAAG,MAAMzB,kBAAkB,CAACO,aAAa,CAACiB,IAAI,CAAC,CAAC,CAAC;MAClEV,UAAU,CAACW,WAAW,CAAC;MAEvB,IAAIA,WAAW,IAAIA,WAAW,CAACC,MAAM,EAAE;QACrC;QACA,MAAMC,qBAAqB,CAACF,WAAW,CAACC,MAAM,CAAC;MACjD;IACF,CAAC,CAAC,OAAOE,GAAQ,EAAE;MACjBhB,QAAQ,CAACgB,GAAG,CAACC,OAAO,IAAI,wBAAwB,CAAC;IACnD,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiB,qBAAqB,GAAG,MAAOD,MAAc,IAAK;IACtDR,sBAAsB,CAAC,IAAI,CAAC;IAC5BE,oBAAoB,CAAC,IAAI,CAAC;IAE1B,IAAI;MACF,MAAMU,gBAAgB,GAAG,MAAM7B,uBAAuB,CAACyB,MAAM,CAAC;MAC9DV,eAAe,CAACc,gBAAgB,CAAC;IACnC,CAAC,CAAC,OAAOF,GAAQ,EAAE;MACjBR,oBAAoB,CAACQ,GAAG,CAACC,OAAO,IAAI,mCAAmC,CAAC;IAC1E,CAAC,SAAS;MACRX,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMa,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC;IACA;IACAT,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMU,UAAU,GAAIC,IAAS,IAAK;IAChC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;IACvB,MAAMC,OAAO,GAAGD,IAAI,CAACE,MAAM,GAAGF,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,IAAIC,IAAI,CAACH,IAAI,CAAC;IAC5D,OAAOC,OAAO,CAACG,kBAAkB,CAAC,OAAO,EAAE;MACzCC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,oBACErC,OAAA,CAACvB,GAAG;IAAA6D,QAAA,gBACFtC,OAAA,CAACtB,UAAU;MAAC6D,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGb5C,OAAA,CAACrB,KAAK;MAACkE,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACzBtC,OAAA,CAACpB,IAAI;QAACoE,SAAS;QAACC,OAAO,EAAE,CAAE;QAACC,UAAU,EAAC,QAAQ;QAAAZ,QAAA,gBAC7CtC,OAAA,CAACpB,IAAI;UAACuE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAf,QAAA,eACvBtC,OAAA,CAACnB,SAAS;YACRyE,SAAS;YACTC,KAAK,EAAC,gBAAgB;YACtBhB,OAAO,EAAC,UAAU;YAClBiB,KAAK,EAAErD,aAAc;YACrBsD,QAAQ,EAAGC,CAAC,IAAKtD,gBAAgB,CAACsD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAClDI,WAAW,EAAC,sBAAsB;YAClCC,UAAU,EAAE;cACVC,YAAY,eACV9D,OAAA,CAACb,UAAU;gBACT4E,OAAO,EAAE5C,mBAAoB;gBAC7B6C,QAAQ,EAAE3D,OAAQ;gBAClB4D,IAAI,EAAC,OAAO;gBAAA3B,QAAA,eAEZtC,OAAA,CAACX,UAAU;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAEhB,CAAE;YACFsB,UAAU,EAAGR,CAAC,IAAK;cACjB,IAAIA,CAAC,CAACS,GAAG,KAAK,OAAO,EAAE;gBACrBhD,mBAAmB,CAAC,CAAC;cACvB;YACF;UAAE;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP5C,OAAA,CAACpB,IAAI;UAACuE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAf,QAAA,eACvBtC,OAAA,CAAClB,MAAM;YACLyD,OAAO,EAAC,WAAW;YACnBwB,OAAO,EAAE5C,mBAAoB;YAC7B6C,QAAQ,EAAE3D,OAAQ;YAClB+D,SAAS,EAAE/D,OAAO,gBAAGL,OAAA,CAACf,gBAAgB;cAACgF,IAAI,EAAE;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5C,OAAA,CAACX,UAAU;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAN,QAAA,EACtE;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAENrC,KAAK,iBACJP,OAAA,CAAChB,KAAK;QAACqF,QAAQ,EAAC,OAAO;QAACxB,EAAE,EAAE;UAAEyB,EAAE,EAAE;QAAE,CAAE;QAAAhC,QAAA,EACnC/B;MAAK;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGPnC,OAAO,iBACNT,OAAA,CAACpB,IAAI;MAACoE,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAX,QAAA,gBACzBtC,OAAA,CAACpB,IAAI;QAACuE,IAAI;QAACC,EAAE,EAAE,EAAG;QAACmB,EAAE,EAAE,CAAE;QAAAjC,QAAA,eACvBtC,OAAA,CAACrB,KAAK;UAACkE,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACzBtC,OAAA,CAACtB,UAAU;YAAC6D,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5C,OAAA,CAACjB,OAAO;YAAC8D,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE1B5C,OAAA,CAACpB,IAAI;YAACoE,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBACzBtC,OAAA,CAACpB,IAAI;cAACuE,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAd,QAAA,eAChBtC,OAAA,CAACvB,GAAG;gBAAC+F,OAAO,EAAC,MAAM;gBAACtB,UAAU,EAAC,QAAQ;gBAAAZ,QAAA,gBACrCtC,OAAA,CAACP,UAAU;kBAACoD,EAAE,EAAE;oBAAE4B,EAAE,EAAE,CAAC;oBAAEC,KAAK,EAAE;kBAAe;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpD5C,OAAA,CAACtB,UAAU;kBAAC6D,OAAO,EAAC,WAAW;kBAAAD,QAAA,EAC5B7B,OAAO,CAACkE,SAAS,IAAI;gBAAO;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEP5C,OAAA,CAACpB,IAAI;cAACuE,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAf,QAAA,gBACvBtC,OAAA,CAACtB,UAAU;gBAAC6D,OAAO,EAAC,OAAO;gBAACmC,KAAK,EAAC,gBAAgB;gBAAApC,QAAA,EAAC;cAEnD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5C,OAAA,CAACtB,UAAU;gBAAC6D,OAAO,EAAC,OAAO;gBAACC,YAAY;gBAAAF,QAAA,EACrC7B,OAAO,CAACN;cAAa;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEP5C,OAAA,CAACpB,IAAI;cAACuE,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAf,QAAA,gBACvBtC,OAAA,CAACtB,UAAU;gBAAC6D,OAAO,EAAC,OAAO;gBAACmC,KAAK,EAAC,gBAAgB;gBAAApC,QAAA,EAAC;cAEnD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5C,OAAA,CAACd,IAAI;gBACHqE,KAAK,EAAE9C,OAAO,CAACmE,MAAM,IAAI,WAAY;gBACrCF,KAAK,EAAEjE,OAAO,CAACmE,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAU;gBAC/DX,IAAI,EAAC;cAAO;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP5C,OAAA,CAACpB,IAAI;cAACuE,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAf,QAAA,gBACvBtC,OAAA,CAACtB,UAAU;gBAAC6D,OAAO,EAAC,OAAO;gBAACmC,KAAK,EAAC,gBAAgB;gBAAApC,QAAA,EAAC;cAEnD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5C,OAAA,CAACtB,UAAU;gBAAC6D,OAAO,EAAC,OAAO;gBAACC,YAAY;gBAAAF,QAAA,EACrCV,UAAU,CAACnB,OAAO,CAACoE,WAAW;cAAC;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEP5C,OAAA,CAACpB,IAAI;cAACuE,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAf,QAAA,gBACvBtC,OAAA,CAACtB,UAAU;gBAAC6D,OAAO,EAAC,OAAO;gBAACmC,KAAK,EAAC,gBAAgB;gBAAApC,QAAA,EAAC;cAEnD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5C,OAAA,CAACtB,UAAU;gBAAC6D,OAAO,EAAC,OAAO;gBAACC,YAAY;gBAAAF,QAAA,EACrCV,UAAU,CAACnB,OAAO,CAACqE,YAAY;cAAC;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEP5C,OAAA,CAACpB,IAAI;cAACuE,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAd,QAAA,gBAChBtC,OAAA,CAACtB,UAAU;gBAAC6D,OAAO,EAAC,OAAO;gBAACmC,KAAK,EAAC,gBAAgB;gBAAApC,QAAA,EAAC;cAEnD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5C,OAAA,CAACtB,UAAU;gBAAC6D,OAAO,EAAC,OAAO;gBAACC,YAAY;gBAAAF,QAAA,EACrC7B,OAAO,CAACsE,QAAQ,IAAI;cAAe;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEP5C,OAAA,CAACpB,IAAI;cAACuE,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAd,QAAA,gBAChBtC,OAAA,CAACtB,UAAU;gBAAC6D,OAAO,EAAC,OAAO;gBAACmC,KAAK,EAAC,gBAAgB;gBAAApC,QAAA,EAAC;cAEnD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5C,OAAA,CAACtB,UAAU;gBAAC6D,OAAO,EAAC,OAAO;gBAACC,YAAY;gBAAAF,QAAA,EACrC7B,OAAO,CAACuE,eAAe,IAAI;cAAM;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP5C,OAAA,CAACvB,GAAG;YAAC6F,EAAE,EAAE,CAAE;YAAAhC,QAAA,gBACTtC,OAAA,CAAClB,MAAM;cACLyD,OAAO,EAAC,WAAW;cACnBmC,KAAK,EAAC,SAAS;cACfpB,SAAS;cACTS,OAAO,EAAEpC,aAAc;cACvBqC,QAAQ,EAAEvD,OAAO,CAACmE,MAAM,KAAK,YAAY,IAAI,CAACjE,YAAY,IAAIA,YAAY,CAACiE,MAAM,KAAK,UAAW;cACjGR,SAAS,eAAEpE,OAAA,CAACT,eAAe;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAN,QAAA,EAChC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRnC,OAAO,CAACmE,MAAM,KAAK,YAAY,iBAC9B5E,OAAA,CAAChB,KAAK;cAACqF,QAAQ,EAAC,MAAM;cAACxB,EAAE,EAAE;gBAAEyB,EAAE,EAAE;cAAE,CAAE;cAAAhC,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR,EACA,CAAC,CAACjC,YAAY,IAAIA,YAAY,CAACiE,MAAM,KAAK,UAAU,kBACnD5E,OAAA,CAAChB,KAAK;cAACqF,QAAQ,EAAC,SAAS;cAACxB,EAAE,EAAE;gBAAEyB,EAAE,EAAE;cAAE,CAAE;cAAAhC,QAAA,EAAC;YAEzC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR,EACA3B,cAAc,iBACbjB,OAAA,CAAChB,KAAK;cAACqF,QAAQ,EAAC,SAAS;cAACxB,EAAE,EAAE;gBAAEyB,EAAE,EAAE;cAAE,CAAE;cAAAhC,QAAA,EAAC;YAEzC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEP5C,OAAA,CAACpB,IAAI;QAACuE,IAAI;QAACC,EAAE,EAAE,EAAG;QAACmB,EAAE,EAAE,CAAE;QAAAjC,QAAA,gBAEvBtC,OAAA,CAACF,uBAAuB;UACtBa,YAAY,EAAEA,YAAa;UAC3BN,OAAO,EAAEQ,mBAAoB;UAC7BN,KAAK,EAAEQ,iBAAiB,IAAIkE,SAAU;UACtCC,SAAS,EAAEA,CAAA,KAAM,CAAAzE,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEa,MAAM,KAAIC,qBAAqB,CAACd,OAAO,CAACa,MAAM;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,EAGDjC,YAAY,IAAIA,YAAY,CAACiE,MAAM,KAAK,UAAU,iBACjD5E,OAAA,CAACrB,KAAK;UAACkE,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEC,EAAE,EAAE,CAAC;YAAEoC,OAAO,EAAE;UAAgB,CAAE;UAAA7C,QAAA,gBACnDtC,OAAA,CAACvB,GAAG;YAAC+F,OAAO,EAAC,MAAM;YAACtB,UAAU,EAAC,QAAQ;YAACH,EAAE,EAAE,CAAE;YAAAT,QAAA,gBAC5CtC,OAAA,CAACL,WAAW;cAAC+E,KAAK,EAAC,SAAS;cAAC7B,EAAE,EAAE;gBAAE4B,EAAE,EAAE;cAAE;YAAE;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9C5C,OAAA,CAACtB,UAAU;cAAC6D,OAAO,EAAC,IAAI;cAAAD,QAAA,EAAC;YAAqB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACN5C,OAAA,CAACtB,UAAU;YAAC6D,OAAO,EAAC,OAAO;YAAC6C,SAAS;YAAA9C,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5C,OAAA,CAACtB,UAAU;YAAC6D,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAC;UAE5B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACP,EAGA,CAACnC,OAAO,IAAI,CAACJ,OAAO,iBACnBL,OAAA,CAACrB,KAAK;MAACkE,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,gBAClBtC,OAAA,CAACtB,UAAU;QAAC6D,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5C,OAAA,CAACtB,UAAU;QAAC6D,OAAO,EAAC,OAAO;QAAC6C,SAAS;QAAA9C,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5C,OAAA,CAACtB,UAAU;QAAC6D,OAAO,EAAC,OAAO;QAAC6C,SAAS;QAAA9C,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5C,OAAA,CAACtB,UAAU;QAAC6D,OAAO,EAAC,OAAO;QAAC6C,SAAS;QAAA9C,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5C,OAAA,CAAChB,KAAK;QAACqF,QAAQ,EAAC,MAAM;QAAA/B,QAAA,EAAC;MAEvB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1C,EAAA,CA3RID,OAAiB;AAAAoF,EAAA,GAAjBpF,OAAiB;AA6RvB,eAAeA,OAAO;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}