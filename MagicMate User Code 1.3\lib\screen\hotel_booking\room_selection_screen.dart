import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:magicmate/utils/Colors.dart';
import 'package:magicmate/model/fontfamily_model.dart';
import 'package:magicmate/model/hotel_model.dart';
import 'package:magicmate/model/room_model.dart';
import 'package:magicmate/controller/hotel_controller.dart';
import 'package:magicmate/screen/hotel_booking/booking_screen.dart';
import 'package:intl/intl.dart';

class RoomSelectionScreen extends StatefulWidget {
  final Hotel hotel;

  const RoomSelectionScreen({
    super.key,
    required this.hotel,
  });

  @override
  State<RoomSelectionScreen> createState() => _RoomSelectionScreenState();
}

class _RoomSelectionScreenState extends State<RoomSelectionScreen> {
  final HotelController hotelController = Get.find<HotelController>();

  DateTime _checkInDate = DateTime.now().add(Duration(days: 1));
  DateTime _checkOutDate = DateTime.now().add(Duration(days: 2));
  int _guests = 1;
  Room? _selectedRoom;

  @override
  void initState() {
    super.initState();
    hotelController.fetchRooms(widget.hotel.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: WhiteColor,
      appBar: AppBar(
        backgroundColor: WhiteColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: BlackColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Select Room',
          style: TextStyle(
            color: BlackColor,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Date and Guest Selection
          Container(
            padding: EdgeInsets.all(16),
            margin: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              children: [
                // Check-in and Check-out dates
                Row(
                  children: [
                    Expanded(
                      child: _buildDateSelector(
                        'Check-in',
                        _checkInDate,
                        (date) => setState(() => _checkInDate = date),
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: _buildDateSelector(
                        'Check-out',
                        _checkOutDate,
                        (date) => setState(() => _checkOutDate = date),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                // Guests
                Row(
                  children: [
                    Text(
                      'Guests',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Spacer(),
                    IconButton(
                      onPressed:
                          _guests > 1 ? () => setState(() => _guests--) : null,
                      icon: Icon(Icons.remove_circle_outline),
                    ),
                    Text(
                      _guests.toString(),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      onPressed: () => setState(() => _guests++),
                      icon: Icon(Icons.add_circle_outline),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Rooms List
          Expanded(
            child: Obx(() {
              if (hotelController.isLoadingRooms.value) {
                return Center(child: CircularProgressIndicator());
              }

              if (hotelController.rooms.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.bed, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'No rooms available',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: EdgeInsets.symmetric(horizontal: 16),
                itemCount: hotelController.rooms.length,
                itemBuilder: (context, index) {
                  final room = hotelController.rooms[index];
                  final isSelected = _selectedRoom?.id == room.id;

                  return Card(
                    margin: EdgeInsets.only(bottom: 16),
                    elevation: isSelected ? 4 : 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(
                        color: isSelected
                            ? gradient.defoultColor
                            : Colors.transparent,
                        width: 2,
                      ),
                    ),
                    child: InkWell(
                      onTap: () => setState(() => _selectedRoom = room),
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        room.name,
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      SizedBox(height: 4),
                                      Text(
                                        room.description,
                                        style: TextStyle(
                                          color: Colors.grey.shade600,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                if (isSelected)
                                  Icon(
                                    Icons.check_circle,
                                    color: gradient.defoultColor,
                                    size: 24,
                                  ),
                              ],
                            ),

                            SizedBox(height: 12),

                            // Room details
                            Row(
                              children: [
                                _buildRoomDetail(
                                    Icons.people, 'Capacity: ${room.capacity}'),
                                SizedBox(width: 16),
                                if (room.size != null)
                                  _buildRoomDetail(
                                      Icons.square_foot, '${room.size} sq ft'),
                                if (room.bedType != null) ...[
                                  SizedBox(width: 16),
                                  _buildRoomDetail(Icons.bed, room.bedType!),
                                ],
                              ],
                            ),

                            SizedBox(height: 12),

                            // Amenities
                            if (room.amenities.isNotEmpty) ...[
                              Wrap(
                                spacing: 8,
                                runSpacing: 4,
                                children: room.amenities.take(4).map((amenity) {
                                  return Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade100,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      amenity,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade700,
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                              SizedBox(height: 12),
                            ],

                            // Price
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '₹${room.price.toStringAsFixed(0)}',
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: gradient.defoultColor,
                                      ),
                                    ),
                                    Text(
                                      'per night',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                  ],
                                ),
                                Text(
                                  'Total: ₹${_calculateTotal(room.price)}',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            }),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: WhiteColor,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade300,
              blurRadius: 10,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: _selectedRoom != null ? _proceedToBooking : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: _selectedRoom != null
                ? gradient.defoultColor
                : Colors.grey.shade400,
            padding: EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Text(
            _selectedRoom != null
                ? 'Book Now - ₹${_calculateTotal(_selectedRoom!.price)}'
                : 'Select a room to continue',
            style: TextStyle(
              color: WhiteColor,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDateSelector(
      String label, DateTime date, Function(DateTime) onDateSelected) {
    return InkWell(
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: date,
          firstDate: DateTime.now(),
          lastDate: DateTime.now().add(Duration(days: 365)),
        );
        if (selectedDate != null) {
          onDateSelected(selectedDate);
        }
      },
      child: Container(
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            SizedBox(height: 4),
            Text(
              DateFormat('MMM dd, yyyy').format(date),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoomDetail(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: Colors.grey.shade600),
        SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  int _calculateTotal(double roomPrice) {
    final nights = _checkOutDate.difference(_checkInDate).inDays;
    return (roomPrice * nights).round();
  }

  void _proceedToBooking() {
    if (_selectedRoom != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => BookingScreen(
            hotel: widget.hotel,
            room: _selectedRoom!,
            checkInDate: _checkInDate,
            checkOutDate: _checkOutDate,
            guests: _guests,
          ),
        ),
      );
    }
  }
}
