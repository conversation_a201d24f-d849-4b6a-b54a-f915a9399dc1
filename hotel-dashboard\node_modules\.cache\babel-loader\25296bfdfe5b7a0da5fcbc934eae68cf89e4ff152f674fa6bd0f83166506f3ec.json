{"ast": null, "code": "import { addLeadingZeros } from \"./_lib/addLeadingZeros.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link formatISO} function options.\n */\n\n/**\n * @name formatISO\n * @category Common Helpers\n * @summary Format the date according to the ISO 8601 standard (https://support.sas.com/documentation/cdl/en/lrdict/64316/HTML/default/viewer.htm#a003169814.htm).\n *\n * @description\n * Return the formatted date string in ISO 8601 format. Options may be passed to control the parts and notations of the date.\n *\n * @param date - The original date\n * @param options - An object with options.\n *\n * @returns The formatted date string (in local time zone)\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18T19:00:52Z'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601, short format (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { format: 'basic' })\n * //=> '20190918T190052'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format, date only:\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { representation: 'date' })\n * //=> '2019-09-18'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format, time only (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { representation: 'time' })\n * //=> '19:00:52Z'\n */\nexport function formatISO(date, options) {\n  const date_ = toDate(date, options?.in);\n  if (isNaN(+date_)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n  const format = options?.format ?? \"extended\";\n  const representation = options?.representation ?? \"complete\";\n  let result = \"\";\n  let tzOffset = \"\";\n  const dateDelimiter = format === \"extended\" ? \"-\" : \"\";\n  const timeDelimiter = format === \"extended\" ? \":\" : \"\";\n\n  // Representation is either 'date' or 'complete'\n  if (representation !== \"time\") {\n    const day = addLeadingZeros(date_.getDate(), 2);\n    const month = addLeadingZeros(date_.getMonth() + 1, 2);\n    const year = addLeadingZeros(date_.getFullYear(), 4);\n\n    // yyyyMMdd or yyyy-MM-dd.\n    result = `${year}${dateDelimiter}${month}${dateDelimiter}${day}`;\n  }\n\n  // Representation is either 'time' or 'complete'\n  if (representation !== \"date\") {\n    // Add the timezone.\n    const offset = date_.getTimezoneOffset();\n    if (offset !== 0) {\n      const absoluteOffset = Math.abs(offset);\n      const hourOffset = addLeadingZeros(Math.trunc(absoluteOffset / 60), 2);\n      const minuteOffset = addLeadingZeros(absoluteOffset % 60, 2);\n      // If less than 0, the sign is +, because it is ahead of time.\n      const sign = offset < 0 ? \"+\" : \"-\";\n      tzOffset = `${sign}${hourOffset}:${minuteOffset}`;\n    } else {\n      tzOffset = \"Z\";\n    }\n    const hour = addLeadingZeros(date_.getHours(), 2);\n    const minute = addLeadingZeros(date_.getMinutes(), 2);\n    const second = addLeadingZeros(date_.getSeconds(), 2);\n\n    // If there's also date, separate it with time with 'T'\n    const separator = result === \"\" ? \"\" : \"T\";\n\n    // Creates a time string consisting of hour, minute, and second, separated by delimiters, if defined.\n    const time = [hour, minute, second].join(timeDelimiter);\n\n    // HHmmss or HH:mm:ss.\n    result = `${result}${separator}${time}${tzOffset}`;\n  }\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default formatISO;", "map": {"version": 3, "names": ["addLeadingZeros", "toDate", "formatISO", "date", "options", "date_", "in", "isNaN", "RangeError", "format", "representation", "result", "tzOffset", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeDelimiter", "day", "getDate", "month", "getMonth", "year", "getFullYear", "offset", "getTimezoneOffset", "absoluteOffset", "Math", "abs", "hourOffset", "trunc", "minuteOffset", "sign", "hour", "getHours", "minute", "getMinutes", "second", "getSeconds", "separator", "time", "join"], "sources": ["G:/linkinblink/hotel-dashboard/node_modules/date-fns/formatISO.js"], "sourcesContent": ["import { addLeadingZeros } from \"./_lib/addLeadingZeros.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link formatISO} function options.\n */\n\n/**\n * @name formatISO\n * @category Common Helpers\n * @summary Format the date according to the ISO 8601 standard (https://support.sas.com/documentation/cdl/en/lrdict/64316/HTML/default/viewer.htm#a003169814.htm).\n *\n * @description\n * Return the formatted date string in ISO 8601 format. Options may be passed to control the parts and notations of the date.\n *\n * @param date - The original date\n * @param options - An object with options.\n *\n * @returns The formatted date string (in local time zone)\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18T19:00:52Z'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601, short format (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { format: 'basic' })\n * //=> '20190918T190052'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format, date only:\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { representation: 'date' })\n * //=> '2019-09-18'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format, time only (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { representation: 'time' })\n * //=> '19:00:52Z'\n */\nexport function formatISO(date, options) {\n  const date_ = toDate(date, options?.in);\n\n  if (isNaN(+date_)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  const format = options?.format ?? \"extended\";\n  const representation = options?.representation ?? \"complete\";\n\n  let result = \"\";\n  let tzOffset = \"\";\n\n  const dateDelimiter = format === \"extended\" ? \"-\" : \"\";\n  const timeDelimiter = format === \"extended\" ? \":\" : \"\";\n\n  // Representation is either 'date' or 'complete'\n  if (representation !== \"time\") {\n    const day = addLeadingZeros(date_.getDate(), 2);\n    const month = addLeadingZeros(date_.getMonth() + 1, 2);\n    const year = addLeadingZeros(date_.getFullYear(), 4);\n\n    // yyyyMMdd or yyyy-MM-dd.\n    result = `${year}${dateDelimiter}${month}${dateDelimiter}${day}`;\n  }\n\n  // Representation is either 'time' or 'complete'\n  if (representation !== \"date\") {\n    // Add the timezone.\n    const offset = date_.getTimezoneOffset();\n\n    if (offset !== 0) {\n      const absoluteOffset = Math.abs(offset);\n      const hourOffset = addLeadingZeros(Math.trunc(absoluteOffset / 60), 2);\n      const minuteOffset = addLeadingZeros(absoluteOffset % 60, 2);\n      // If less than 0, the sign is +, because it is ahead of time.\n      const sign = offset < 0 ? \"+\" : \"-\";\n\n      tzOffset = `${sign}${hourOffset}:${minuteOffset}`;\n    } else {\n      tzOffset = \"Z\";\n    }\n\n    const hour = addLeadingZeros(date_.getHours(), 2);\n    const minute = addLeadingZeros(date_.getMinutes(), 2);\n    const second = addLeadingZeros(date_.getSeconds(), 2);\n\n    // If there's also date, separate it with time with 'T'\n    const separator = result === \"\" ? \"\" : \"T\";\n\n    // Creates a time string consisting of hour, minute, and second, separated by delimiters, if defined.\n    const time = [hour, minute, second].join(timeDelimiter);\n\n    // HHmmss or HH:mm:ss.\n    result = `${result}${separator}${time}${tzOffset}`;\n  }\n\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default formatISO;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACvC,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EAEvC,IAAIC,KAAK,CAAC,CAACF,KAAK,CAAC,EAAE;IACjB,MAAM,IAAIG,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EAEA,MAAMC,MAAM,GAAGL,OAAO,EAAEK,MAAM,IAAI,UAAU;EAC5C,MAAMC,cAAc,GAAGN,OAAO,EAAEM,cAAc,IAAI,UAAU;EAE5D,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAG,EAAE;EAEjB,MAAMC,aAAa,GAAGJ,MAAM,KAAK,UAAU,GAAG,GAAG,GAAG,EAAE;EACtD,MAAMK,aAAa,GAAGL,MAAM,KAAK,UAAU,GAAG,GAAG,GAAG,EAAE;;EAEtD;EACA,IAAIC,cAAc,KAAK,MAAM,EAAE;IAC7B,MAAMK,GAAG,GAAGf,eAAe,CAACK,KAAK,CAACW,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/C,MAAMC,KAAK,GAAGjB,eAAe,CAACK,KAAK,CAACa,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IACtD,MAAMC,IAAI,GAAGnB,eAAe,CAACK,KAAK,CAACe,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;;IAEpD;IACAT,MAAM,GAAG,GAAGQ,IAAI,GAAGN,aAAa,GAAGI,KAAK,GAAGJ,aAAa,GAAGE,GAAG,EAAE;EAClE;;EAEA;EACA,IAAIL,cAAc,KAAK,MAAM,EAAE;IAC7B;IACA,MAAMW,MAAM,GAAGhB,KAAK,CAACiB,iBAAiB,CAAC,CAAC;IAExC,IAAID,MAAM,KAAK,CAAC,EAAE;MAChB,MAAME,cAAc,GAAGC,IAAI,CAACC,GAAG,CAACJ,MAAM,CAAC;MACvC,MAAMK,UAAU,GAAG1B,eAAe,CAACwB,IAAI,CAACG,KAAK,CAACJ,cAAc,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;MACtE,MAAMK,YAAY,GAAG5B,eAAe,CAACuB,cAAc,GAAG,EAAE,EAAE,CAAC,CAAC;MAC5D;MACA,MAAMM,IAAI,GAAGR,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;MAEnCT,QAAQ,GAAG,GAAGiB,IAAI,GAAGH,UAAU,IAAIE,YAAY,EAAE;IACnD,CAAC,MAAM;MACLhB,QAAQ,GAAG,GAAG;IAChB;IAEA,MAAMkB,IAAI,GAAG9B,eAAe,CAACK,KAAK,CAAC0B,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACjD,MAAMC,MAAM,GAAGhC,eAAe,CAACK,KAAK,CAAC4B,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;IACrD,MAAMC,MAAM,GAAGlC,eAAe,CAACK,KAAK,CAAC8B,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;;IAErD;IACA,MAAMC,SAAS,GAAGzB,MAAM,KAAK,EAAE,GAAG,EAAE,GAAG,GAAG;;IAE1C;IACA,MAAM0B,IAAI,GAAG,CAACP,IAAI,EAAEE,MAAM,EAAEE,MAAM,CAAC,CAACI,IAAI,CAACxB,aAAa,CAAC;;IAEvD;IACAH,MAAM,GAAG,GAAGA,MAAM,GAAGyB,SAAS,GAAGC,IAAI,GAAGzB,QAAQ,EAAE;EACpD;EAEA,OAAOD,MAAM;AACf;;AAEA;AACA,eAAeT,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}