# 🔥 Firebase Terminal Setup Guide - Complete Automation

## 🚀 **ONE-COMMAND SETUP**

### **Quick Start (Recommended):**
```bash
# Navigate to your project directory
cd "MagicMate User Code 1.3"

# Make the master script executable and run it
chmod +x setup_firebase_complete.sh
./setup_firebase_complete.sh
```

**This single command will:**
- ✅ Install Firebase CLI
- ✅ Login to Firebase
- ✅ Set up project linkinblink-f544a
- ✅ Create all Firestore collections
- ✅ Configure Authentication
- ✅ Set up Cloud Messaging
- ✅ Deploy security rules
- ✅ Create test users
- ✅ Generate complete setup summary

## 📋 **STEP-BY-STEP MANUAL SETUP**

### **Prerequisites:**
```bash
# Check if Node.js is installed
node --version

# Check if npm is installed
npm --version

# If not installed, install Node.js from https://nodejs.org/
```

### **Step 1: Firebase CLI Setup**
```bash
# Make script executable
chmod +x firebase_terminal_setup.sh

# Run Firebase CLI setup
./firebase_terminal_setup.sh
```

**This will:**
- Install Firebase CLI globally
- Login to Firebase (opens browser)
- Set project to linkinblink-f544a
- Create firebase.json configuration
- Create Firestore and Storage security rules
- Deploy rules to Firebase

### **Step 2: Create Firestore Collections**
```bash
# Install Node.js dependencies
npm install firebase-admin

# Create all required collections
node create_firestore_collections.js
```

**This creates 8 collections:**
- MagicUser
- MagicOrganizer  
- Magic_Organization_rooms (with messages subcollection)
- events
- bookings
- hotels
- hotel_bookings
- notifications

### **Step 3: Setup Authentication**
```bash
# Make script executable
chmod +x setup_firebase_auth.sh

# Run authentication setup
./setup_firebase_auth.sh

# Create test users
node create_test_users.js
```

**This configures:**
- Email/Password authentication
- Phone authentication
- Test user accounts
- Custom claims for organizers

### **Step 4: Configure Cloud Messaging**
```bash
# Make script executable
chmod +x configure_cloud_messaging.sh

# Run FCM setup
./configure_cloud_messaging.sh

# Test notifications (update with real tokens)
npm run test-notifications

# Setup topics
npm run setup-topics
```

**This sets up:**
- FCM API
- Notification service
- Topic management
- Test scripts

## 🧪 **TESTING YOUR SETUP**

### **Test Firestore:**
```bash
# Verify collections exist
node -e "
const admin = require('firebase-admin');
admin.initializeApp();
const db = admin.firestore();
db.listCollections().then(collections => {
  console.log('Collections:', collections.map(c => c.id));
  process.exit(0);
});
"
```

### **Test Authentication:**
```bash
# List test users
node -e "
const admin = require('firebase-admin');
admin.initializeApp();
admin.auth().listUsers().then(result => {
  console.log('Users:', result.users.map(u => u.email));
  process.exit(0);
});
"
```

### **Test Cloud Messaging:**
```bash
# Test notification sending (update with real FCM token)
npm run test-notifications
```

## 📱 **MOBILE APP INTEGRATION**

### **After Firebase Setup:**

#### **1. Update Firebase Configuration:**
Your mobile apps already have the correct configuration in:
- `lib/firebase_options.dart` (User App)
- `lib/firebase_options.dart` (Organizer App)

#### **2. Verify Project ID:**
```dart
// Should match in firebase_options.dart
projectId: 'linkinblink-f544a'
```

#### **3. Test FCM Tokens:**
```dart
// In your mobile app, get FCM token
FirebaseMessaging.instance.getToken().then((token) {
  print('FCM Token: $token');
  // Use this token in test_notifications.js
});
```

## 🔧 **TROUBLESHOOTING**

### **Common Issues:**

#### **"Firebase CLI not found"**
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Or using curl
curl -sL https://firebase.tools | bash
```

#### **"Permission denied"**
```bash
# Make scripts executable
chmod +x *.sh
```

#### **"Project not found"**
```bash
# Login to Firebase
firebase login

# Set correct project
firebase use linkinblink-f544a
```

#### **"Authentication failed"**
```bash
# Re-login to Firebase
firebase logout
firebase login
```

#### **"Node.js modules not found"**
```bash
# Install dependencies
npm install firebase-admin
```

### **Verify Setup:**
```bash
# Check Firebase project
firebase projects:list

# Check current project
firebase use

# Check Firestore rules
firebase firestore:rules:get

# Check Storage rules
firebase storage:rules:get
```

## 📊 **FIREBASE CONSOLE VERIFICATION**

### **After Setup, Verify in Firebase Console:**

#### **1. Firestore Database:**
```
URL: https://console.firebase.google.com/project/linkinblink-f544a/firestore
Check: 8 collections with sample data
```

#### **2. Authentication:**
```
URL: https://console.firebase.google.com/project/linkinblink-f544a/authentication
Check: Email/Password and Phone providers enabled
Check: Test users exist
```

#### **3. Cloud Messaging:**
```
URL: https://console.firebase.google.com/project/linkinblink-f544a/settings/cloudmessaging
Check: FCM API enabled
Action: Copy Server Key for backend
```

#### **4. Storage:**
```
URL: https://console.firebase.google.com/project/linkinblink-f544a/storage
Check: Storage bucket created
Check: Security rules deployed
```

## 🎯 **NEXT STEPS AFTER SETUP**

### **1. Get FCM Server Key:**
```
1. Go to Firebase Console → Project Settings → Cloud Messaging
2. Copy "Server key"
3. Add to your backend configuration
```

### **2. Update Mobile Apps:**
```
1. Verify firebase_options.dart is correct
2. Test FCM token generation
3. Test user registration/login
4. Test push notifications
```

### **3. Backend Integration:**
```
1. Add FCM Server Key to admin panel settings
2. Deploy notification_service.php to your server
3. Test notification sending from backend
```

### **4. Production Deployment:**
```
1. Review and adjust security rules
2. Configure authorized domains
3. Set up monitoring and analytics
4. Deploy mobile apps to stores
```

## 📞 **SUPPORT COMMANDS**

### **Useful Firebase CLI Commands:**
```bash
# Check Firebase status
firebase --version

# List projects
firebase projects:list

# Get project info
firebase use

# Deploy specific services
firebase deploy --only firestore
firebase deploy --only storage
firebase deploy --only functions

# View logs
firebase functions:log

# Open Firebase Console
firebase open
```

### **Useful Node.js Scripts:**
```bash
# Available npm scripts
npm run create-collections
npm run create-test-users
npm run test-notifications
npm run setup-topics
npm run test-fcm
```

## 🎉 **SUCCESS INDICATORS**

### **Setup is successful when:**
- ✅ Firebase CLI login works
- ✅ Project linkinblink-f544a is active
- ✅ 8 Firestore collections exist with data
- ✅ Authentication providers are enabled
- ✅ Test users can be created
- ✅ FCM API is enabled
- ✅ Security rules are deployed
- ✅ Mobile apps can connect to Firebase

### **Final Verification:**
```bash
# Run complete test
./setup_firebase_complete.sh

# Check summary
cat firebase_setup_summary.md
```

**Your Firebase project will be 100% ready for production use!** 🚀🔥
