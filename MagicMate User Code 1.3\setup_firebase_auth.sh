#!/bin/bash

# Firebase Authentication Setup Script
# This script configures Firebase Authentication for MagicMate

echo "🔐 Firebase Authentication Setup"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

PROJECT_ID="linkinblink-f544a"

echo -e "${BLUE}Setting up Authentication for project: $PROJECT_ID${NC}"
echo ""

# Step 1: Enable Authentication
echo -e "${YELLOW}Step 1: Enabling Firebase Authentication...${NC}"
gcloud services enable identitytoolkit.googleapis.com --project=$PROJECT_ID

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Firebase Authentication enabled${NC}"
else
    echo -e "${RED}❌ Failed to enable Firebase Authentication${NC}"
    exit 1
fi

# Step 2: Configure Authentication providers using Firebase CLI
echo -e "${YELLOW}Step 2: Configuring Authentication providers...${NC}"

# Create auth configuration file
cat > auth_config.json << 'EOF'
{
  "signIn": {
    "allowDuplicateEmails": false,
    "anonymous": {
      "enabled": true
    },
    "email": {
      "enabled": true,
      "passwordRequired": true
    },
    "phone": {
      "enabled": true,
      "testPhoneNumbers": {
        "+************": "123456"
      }
    }
  },
  "blockingFunctions": {},
  "mfa": {
    "state": "DISABLED"
  },
  "passwordPolicy": {
    "enforcementState": "ENFORCE",
    "constraints": {
      "requireUppercase": false,
      "requireLowercase": false,
      "requireNumeric": false,
      "requireNonAlphanumeric": false,
      "minLength": 6,
      "maxLength": 4096
    }
  },
  "recaptchaConfig": {
    "emailPasswordEnforcementState": "OFF",
    "managedRules": [],
    "useAccountDefender": false
  },
  "smsRegionConfig": {
    "allowByDefault": {
      "disallowedRegions": []
    }
  },
  "multiTenant": {
    "allowTenants": false
  },
  "authorizedDomains": [
    "localhost",
    "linkinblink.wipstertechnologies.com",
    "linkinblink-f544a.firebaseapp.com",
    "linkinblink-f544a.web.app"
  ]
}
EOF

echo -e "${GREEN}✅ Created authentication configuration${NC}"

# Step 3: Apply authentication configuration
echo -e "${YELLOW}Step 3: Applying authentication configuration...${NC}"

# Note: Firebase CLI doesn't directly support auth config import
# This would typically be done through the Firebase Console
echo -e "${BLUE}Authentication providers to enable manually in Firebase Console:${NC}"
echo "1. Email/Password - ✅ Enable"
echo "2. Phone - ✅ Enable"
echo "3. Anonymous - ✅ Enable (optional)"
echo ""
echo -e "${BLUE}Authorized domains to add:${NC}"
echo "- localhost"
echo "- linkinblink.wipstertechnologies.com"
echo "- linkinblink-f544a.firebaseapp.com"
echo "- linkinblink-f544a.web.app"

# Step 4: Create test users script
echo -e "${YELLOW}Step 4: Creating test users setup script...${NC}"

cat > create_test_users.js << 'EOF'
// Create Test Users Script
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
admin.initializeApp();

const auth = admin.auth();

async function createTestUsers() {
  console.log('👥 Creating test users...');
  
  try {
    // Create test user
    const testUser = await auth.createUser({
      uid: 'test_user_1',
      email: '<EMAIL>',
      password: 'password123',
      displayName: 'Test User',
      phoneNumber: '+************',
      emailVerified: true,
      disabled: false
    });
    console.log('✅ Created test user:', testUser.uid);

    // Create test organizer
    const testOrganizer = await auth.createUser({
      uid: 'test_organizer_1',
      email: '<EMAIL>',
      password: 'password123',
      displayName: 'Test Organizer',
      phoneNumber: '+************',
      emailVerified: true,
      disabled: false
    });
    console.log('✅ Created test organizer:', testOrganizer.uid);

    // Set custom claims for organizer
    await auth.setCustomUserClaims(testOrganizer.uid, {
      role: 'organizer',
      permissions: ['create_events', 'manage_bookings']
    });
    console.log('✅ Set custom claims for organizer');

    console.log('');
    console.log('🎉 Test users created successfully!');
    console.log('');
    console.log('📧 Test User Credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');
    console.log('Phone: +************');
    console.log('');
    console.log('🏢 Test Organizer Credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');
    console.log('Phone: +************');

  } catch (error) {
    console.error('❌ Error creating test users:', error);
  }
}

// Run if called directly
if (require.main === module) {
  createTestUsers().then(() => {
    console.log('✅ Test users setup complete!');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Test users setup failed:', error);
    process.exit(1);
  });
}

module.exports = { createTestUsers };
EOF

echo -e "${GREEN}✅ Created test users setup script${NC}"

# Step 5: Create authentication helper functions
echo -e "${YELLOW}Step 5: Creating authentication helper functions...${NC}"

cat > auth_helpers.js << 'EOF'
// Firebase Authentication Helper Functions
const admin = require('firebase-admin');

class AuthHelpers {
  
  // Verify ID token
  static async verifyToken(idToken) {
    try {
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      return { success: true, user: decodedToken };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Create custom token
  static async createCustomToken(uid, additionalClaims = {}) {
    try {
      const customToken = await admin.auth().createCustomToken(uid, additionalClaims);
      return { success: true, token: customToken };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Get user by email
  static async getUserByEmail(email) {
    try {
      const user = await admin.auth().getUserByEmail(email);
      return { success: true, user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Get user by phone
  static async getUserByPhone(phoneNumber) {
    try {
      const user = await admin.auth().getUserByPhoneNumber(phoneNumber);
      return { success: true, user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Update user
  static async updateUser(uid, properties) {
    try {
      const user = await admin.auth().updateUser(uid, properties);
      return { success: true, user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Delete user
  static async deleteUser(uid) {
    try {
      await admin.auth().deleteUser(uid);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Set custom claims
  static async setCustomClaims(uid, claims) {
    try {
      await admin.auth().setCustomUserClaims(uid, claims);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // List users
  static async listUsers(maxResults = 1000) {
    try {
      const listUsersResult = await admin.auth().listUsers(maxResults);
      return { success: true, users: listUsersResult.users };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

module.exports = AuthHelpers;
EOF

echo -e "${GREEN}✅ Created authentication helper functions${NC}"

# Step 6: Install required dependencies
echo -e "${YELLOW}Step 6: Installing required Node.js dependencies...${NC}"

# Create package.json if it doesn't exist
if [ ! -f package.json ]; then
    cat > package.json << 'EOF'
{
  "name": "magicmate-firebase-setup",
  "version": "1.0.0",
  "description": "Firebase setup for MagicMate",
  "main": "index.js",
  "scripts": {
    "create-collections": "node create_firestore_collections.js",
    "create-test-users": "node create_test_users.js",
    "test-auth": "node test_auth.js"
  },
  "dependencies": {
    "firebase-admin": "^12.0.0"
  }
}
EOF
fi

npm install firebase-admin

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Installed Node.js dependencies${NC}"
else
    echo -e "${RED}❌ Failed to install dependencies${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Firebase Authentication Setup Complete!${NC}"
echo ""
echo -e "${BLUE}Next Steps:${NC}"
echo "1. Go to Firebase Console → Authentication → Sign-in method"
echo "2. Enable Email/Password provider"
echo "3. Enable Phone provider"
echo "4. Add authorized domains"
echo "5. Run: node create_test_users.js"
echo ""
echo -e "${YELLOW}Firebase Console URL:${NC}"
echo "https://console.firebase.google.com/project/$PROJECT_ID/authentication"
echo ""
echo -e "${BLUE}Test Credentials (after running create_test_users.js):${NC}"
echo "User: <EMAIL> / password123"
echo "Organizer: <EMAIL> / password123"
