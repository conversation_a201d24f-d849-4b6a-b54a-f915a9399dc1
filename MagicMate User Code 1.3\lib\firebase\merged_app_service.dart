import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import '../model/hotel_model.dart';
import '../model/booking_model.dart';
import '../Api/data_store.dart';

/// Firebase service for the merged Event + Hotel application
/// Handles both event management and hotel booking functionalities
class MergedAppFirebaseService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  // Collection names for the merged app
  static const String usersCollection = 'MagicUser';
  static const String eventsCollection = 'events';
  static const String hotelsCollection = 'hotels';
  static const String bookingsCollection = 'bookings';
  static const String roomsCollection = 'rooms';
  static const String organizationsCollection = 'Magic_Organization_rooms';

  /// Initialize Firebase services for the merged app
  static Future<void> initializeServices() async {
    try {
      // Request notification permissions
      await _messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      // Get FCM token
      String? token = await _messaging.getToken();
      debugPrint('FCM Token: $token');

      // Set up background message handler
      FirebaseMessaging.onBackgroundMessage(
          _firebaseMessagingBackgroundHandler);
    } catch (e) {
      debugPrint('Firebase initialization error: $e');
    }
  }

  /// Background message handler
  static Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    debugPrint('Handling background message: ${message.messageId}');
  }

  /// User Management for merged app
  static Future<void> createOrUpdateUser({
    required String uid,
    required String name,
    required String email,
    required String mobile,
    String? profilePic,
  }) async {
    try {
      String? fcmToken = await _messaging.getToken();

      await _firestore.collection(usersCollection).doc(uid).set({
        'uid': uid,
        'name': name,
        'email': email,
        'mobile': mobile,
        'profilePic': profilePic ?? '',
        'fcmToken': fcmToken,
        'isOnline': true,
        'lastActive': FieldValue.serverTimestamp(),
        'createdAt': FieldValue.serverTimestamp(),
        'appType': 'merged_event_hotel', // Identifier for merged app
      }, SetOptions(merge: true));

      debugPrint('User created/updated successfully: $uid');
    } catch (e) {
      debugPrint('Error creating/updating user: $e');
      throw e;
    }
  }

  /// Hotel Management
  static Future<List<Hotel>> fetchHotels({
    String? city,
    double? minPrice,
    double? maxPrice,
    List<String>? amenities,
  }) async {
    try {
      Query query = _firestore
          .collection(hotelsCollection)
          .where('status', isEqualTo: 'active');

      if (city != null && city.isNotEmpty) {
        query = query.where('city', isEqualTo: city);
      }

      if (minPrice != null) {
        query = query.where('minPrice', isGreaterThanOrEqualTo: minPrice);
      }

      if (maxPrice != null) {
        query = query.where('maxPrice', isLessThanOrEqualTo: maxPrice);
      }

      QuerySnapshot snapshot = await query.get();

      return snapshot.docs.map((doc) => Hotel.fromFirestore(doc)).toList();
    } catch (e) {
      debugPrint('Error fetching hotels: $e');
      return [];
    }
  }

  /// Create Hotel Booking
  static Future<String?> createBooking(Booking booking) async {
    try {
      DocumentReference docRef =
          await _firestore.collection(bookingsCollection).add({
        'userId': booking.userId,
        'hotelId': booking.hotelId,
        'hotelName': booking.hotelName,
        'roomId': booking.roomId,
        'roomName': booking.roomName,
        'checkIn': Timestamp.fromDate(booking.checkIn),
        'checkOut': Timestamp.fromDate(booking.checkOut),
        'guests': booking.guests,
        'totalPrice': booking.totalPrice,
        'guestDetails': booking.guestDetails,
        'status': booking.status,
        'paymentStatus': booking.paymentStatus,
        'isAadhaarVerified': booking.isAadhaarVerified,
        'createdAt': FieldValue.serverTimestamp(),
        'appType': 'merged_event_hotel',
      });

      debugPrint('Booking created successfully: ${docRef.id}');
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating booking: $e');
      return null;
    }
  }

  /// Fetch User Bookings
  static Future<List<Booking>> fetchUserBookings(String userId) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection(bookingsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return Booking.fromMap(data);
      }).toList();
    } catch (e) {
      debugPrint('Error fetching user bookings: $e');
      return [];
    }
  }

  /// Event Management (for existing event functionality)
  static Future<void> createEvent({
    required String title,
    required String description,
    required DateTime eventDate,
    required String location,
    required double price,
    required String organizerId,
  }) async {
    try {
      await _firestore.collection(eventsCollection).add({
        'title': title,
        'description': description,
        'eventDate': Timestamp.fromDate(eventDate),
        'location': location,
        'price': price,
        'organizerId': organizerId,
        'status': 'active',
        'createdAt': FieldValue.serverTimestamp(),
        'appType': 'merged_event_hotel',
      });

      debugPrint('Event created successfully');
    } catch (e) {
      debugPrint('Error creating event: $e');
      throw e;
    }
  }

  /// Upload Image to Firebase Storage
  static Future<String?> uploadImage(Uint8List imageData, String path) async {
    try {
      Reference ref = _storage.ref().child(path);
      UploadTask uploadTask = ref.putData(imageData);
      TaskSnapshot snapshot = await uploadTask;
      String downloadUrl = await snapshot.ref.getDownloadURL();

      debugPrint('Image uploaded successfully: $downloadUrl');
      return downloadUrl;
    } catch (e) {
      debugPrint('Error uploading image: $e');
      return null;
    }
  }

  /// Send Push Notification
  static Future<void> sendNotification({
    required String userId,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Get user's FCM token
      DocumentSnapshot userDoc =
          await _firestore.collection(usersCollection).doc(userId).get();

      if (userDoc.exists) {
        Map<String, dynamic> userData = userDoc.data() as Map<String, dynamic>;
        String? fcmToken = userData['fcmToken'];

        if (fcmToken != null) {
          // Here you would typically use Firebase Cloud Functions or a server
          // to send the actual push notification
          debugPrint('Would send notification to token: $fcmToken');
        }
      }
    } catch (e) {
      debugPrint('Error sending notification: $e');
    }
  }

  /// Update User Online Status
  static Future<void> updateUserOnlineStatus(
      String userId, bool isOnline) async {
    try {
      await _firestore.collection(usersCollection).doc(userId).update({
        'isOnline': isOnline,
        'lastActive': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error updating user status: $e');
    }
  }

  /// Get Current User Data
  static Future<Map<String, dynamic>?> getCurrentUserData() async {
    try {
      var userData = getData.read("UserLogin");
      if (userData != null && userData['id'] != null) {
        DocumentSnapshot doc = await _firestore
            .collection(usersCollection)
            .doc(userData['id'])
            .get();

        if (doc.exists) {
          return doc.data() as Map<String, dynamic>;
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error getting current user data: $e');
      return null;
    }
  }

  /// Clean up resources
  static Future<void> dispose() async {
    // Clean up any listeners or resources
    debugPrint('Firebase service disposed');
  }
}
