// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_methods
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyApQJKR1WD9rfXl28b52jwh4of3tXAeKN8',
    appId: '1:73203809024:web:6daf906f00dc3f8fa051e5',
    messagingSenderId: '73203809024',
    projectId: 'link-in-blink',
    authDomain: 'link-in-blink.firebaseapp.com',
    storageBucket: 'link-in-blink.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyApQJKR1WD9rfXl28b52jwh4of3tXAeKN8',
    appId: '1:73203809024:android:6daf906f00dc3f8fa051e5',
    messagingSenderId: '73203809024',
    projectId: 'link-in-blink',
    storageBucket: 'link-in-blink.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyApQJKR1WD9rfXl28b52jwh4of3tXAeKN8',
    appId: '1:73203809024:ios:6daf906f00dc3f8fa051e5',
    messagingSenderId: '73203809024',
    projectId: 'link-in-blink',
    storageBucket: 'link-in-blink.firebasestorage.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyApQJKR1WD9rfXl28b52jwh4of3tXAeKN8',
    appId: '1:73203809024:macos:6daf906f00dc3f8fa051e5',
    messagingSenderId: '73203809024',
    projectId: 'link-in-blink',
    storageBucket: 'link-in-blink.firebasestorage.app',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyApQJKR1WD9rfXl28b52jwh4of3tXAeKN8',
    appId: '1:73203809024:windows:6daf906f00dc3f8fa051e5',
    messagingSenderId: '73203809024',
    projectId: 'link-in-blink',
    storageBucket: 'link-in-blink.firebasestorage.app',
  );
}
