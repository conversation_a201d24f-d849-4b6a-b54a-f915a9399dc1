<?php
require "filemanager/evconfing.php";
require "filemanager/event.php";

// Check if admin is logged in
if (!isset($_SESSION['evename']) || $_SESSION['stype'] != 'mowner') {
    header("Location: index.php");
    exit();
}

header('Content-Type: application/json');

// Test settings save functionality
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get current settings
        $currentSettings = $evmulti->query("SELECT * FROM tbl_setting WHERE id = 1")->fetch_assoc();
        
        if (!$currentSettings) {
            echo json_encode([
                "status" => "error",
                "message" => "No settings found in database"
            ]);
            exit();
        }
        
        // Test data
        $testData = [
            "type" => "edit_setting",
            "id" => "1",
            "webname" => $currentSettings['webname'],
            "timezone" => $currentSettings['timezone'],
            "currency" => $currentSettings['currency'],
            "pstore" => $currentSettings['pstore'],
            "one_key" => $currentSettings['one_key'],
            "one_hash" => $currentSettings['one_hash'],
            "s_key" => $currentSettings['s_key'],
            "s_hash" => $currentSettings['s_hash'],
            "scredit" => $currentSettings['scredit'],
            "rcredit" => $currentSettings['rcredit'],
            "tax" => $currentSettings['tax'],
            "sms_type" => "BulkSMSInd",
            "auth_key" => $currentSettings['auth_key'],
            "otp_id" => $currentSettings['otp_id'],
            "acc_id" => $currentSettings['acc_id'],
            "auth_token" => $currentSettings['auth_token'],
            "twilio_number" => $currentSettings['twilio_number'],
            "otp_auth" => "No",
            "bulksmsind_username" => "hmescan",
            "bulksmsind_api_key" => "e0117592-f761-4393-9772-31d4c0eb41cf",
            "bulksmsind_sender" => "HMESCA",
            "bulksmsind_pe_id" => "1701163403411961935",
            "bulksmsind_template_id" => "1707174566472523986"
        ];
        
        // Simulate POST data
        $_POST = $testData;
        $_FILES["weblogo"]["name"] = ""; // No logo upload
        
        // Include the manager.php to process the settings
        ob_start();
        include "filemanager/manager.php";
        $output = ob_get_clean();
        
        // Check if settings were updated
        $updatedSettings = $evmulti->query("SELECT * FROM tbl_setting WHERE id = 1")->fetch_assoc();
        
        $response = [
            "status" => "success",
            "message" => "Settings save test completed",
            "before" => [
                "sms_type" => $currentSettings['sms_type'],
                "bulksmsind_username" => $currentSettings['bulksmsind_username'],
                "bulksmsind_sender" => $currentSettings['bulksmsind_sender']
            ],
            "after" => [
                "sms_type" => $updatedSettings['sms_type'],
                "bulksmsind_username" => $updatedSettings['bulksmsind_username'],
                "bulksmsind_sender" => $updatedSettings['bulksmsind_sender']
            ],
            "updated" => [
                "sms_type_changed" => $currentSettings['sms_type'] !== $updatedSettings['sms_type'],
                "bulksmsind_username_changed" => $currentSettings['bulksmsind_username'] !== $updatedSettings['bulksmsind_username'],
                "bulksmsind_sender_changed" => $currentSettings['bulksmsind_sender'] !== $updatedSettings['bulksmsind_sender']
            ]
        ];
        
        echo json_encode($response, JSON_PRETTY_PRINT);
        
    } catch (Exception $e) {
        echo json_encode([
            "status" => "error",
            "message" => $e->getMessage()
        ]);
    }
} else {
    // GET request - show current settings
    try {
        $settings = $evmulti->query("SELECT * FROM tbl_setting WHERE id = 1")->fetch_assoc();
        
        $response = [
            "status" => "info",
            "message" => "Current settings",
            "settings" => [
                "webname" => $settings['webname'],
                "sms_type" => $settings['sms_type'],
                "bulksmsind_username" => $settings['bulksmsind_username'],
                "bulksmsind_api_key" => substr($settings['bulksmsind_api_key'], 0, 10) . "...",
                "bulksmsind_sender" => $settings['bulksmsind_sender'],
                "bulksmsind_pe_id" => $settings['bulksmsind_pe_id'],
                "bulksmsind_template_id" => $settings['bulksmsind_template_id'],
                "otp_auth" => $settings['otp_auth']
            ],
            "test_instructions" => [
                "1" => "Send POST request to test settings save",
                "2" => "Check before/after values",
                "3" => "Verify BulkSMSInd settings are saved"
            ]
        ];
        
        echo json_encode($response, JSON_PRETTY_PRINT);
        
    } catch (Exception $e) {
        echo json_encode([
            "status" => "error",
            "message" => $e->getMessage()
        ]);
    }
}
?>
