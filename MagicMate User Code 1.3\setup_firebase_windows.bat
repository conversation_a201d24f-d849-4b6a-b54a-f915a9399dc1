@echo off
echo 🔥 FIREBASE SETUP FOR MAGICMATE - WINDOWS
echo ========================================
echo.

REM Colors for Windows (limited)
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

echo %BLUE%Project ID: linkinblink-f544a%NC%
echo %BLUE%Platform: Windows%NC%
echo.

REM Step 1: Check Node.js
echo %YELLOW%Step 1: Checking Node.js installation...%NC%
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ Node.js is not installed%NC%
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
) else (
    echo %GREEN%✅ Node.js is installed%NC%
    node --version
)

REM Step 2: Check npm
echo %YELLOW%Step 2: Checking npm installation...%NC%
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ npm is not installed%NC%
    pause
    exit /b 1
) else (
    echo %GREEN%✅ npm is installed%NC%
    npm --version
)

REM Step 3: Install Firebase CLI
echo %YELLOW%Step 3: Installing Firebase CLI...%NC%
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing Firebase CLI...
    npm install -g firebase-tools
    if %errorlevel% neq 0 (
        echo %RED%❌ Failed to install Firebase CLI%NC%
        pause
        exit /b 1
    )
) else (
    echo %GREEN%✅ Firebase CLI already installed%NC%
    firebase --version
)

REM Step 4: Login to Firebase
echo %YELLOW%Step 4: Logging into Firebase...%NC%
echo This will open a browser window for authentication
firebase login --interactive
if %errorlevel% neq 0 (
    echo %RED%❌ Failed to login to Firebase%NC%
    pause
    exit /b 1
) else (
    echo %GREEN%✅ Successfully logged into Firebase%NC%
)

REM Step 5: Set Firebase project
echo %YELLOW%Step 5: Setting Firebase project...%NC%
firebase use linkinblink-f544a
if %errorlevel% neq 0 (
    echo %RED%❌ Failed to set project%NC%
    echo Creating project...
    firebase projects:create linkinblink-f544a --display-name "LinkinBlink MagicMate"
    firebase use linkinblink-f544a
)

REM Step 6: Install Node.js dependencies
echo %YELLOW%Step 6: Installing Node.js dependencies...%NC%
npm install firebase-admin
if %errorlevel% neq 0 (
    echo %RED%❌ Failed to install dependencies%NC%
    pause
    exit /b 1
) else (
    echo %GREEN%✅ Dependencies installed successfully%NC%
)

REM Step 7: Initialize Firebase
echo %YELLOW%Step 7: Initializing Firebase...%NC%
firebase init firestore --project linkinblink-f544a
firebase init storage --project linkinblink-f544a

REM Step 8: Create Firestore collections
echo %YELLOW%Step 8: Creating Firestore collections...%NC%
node create_firestore_collections.js
if %errorlevel% neq 0 (
    echo %RED%❌ Failed to create collections%NC%
) else (
    echo %GREEN%✅ Firestore collections created%NC%
)

REM Step 9: Create test users
echo %YELLOW%Step 9: Creating test users...%NC%
node create_test_users.js
if %errorlevel% neq 0 (
    echo %RED%❌ Failed to create test users%NC%
) else (
    echo %GREEN%✅ Test users created%NC%
)

REM Step 10: Setup topics
echo %YELLOW%Step 10: Setting up FCM topics...%NC%
node manage_topics.js
if %errorlevel% neq 0 (
    echo %RED%❌ Failed to setup topics%NC%
) else (
    echo %GREEN%✅ FCM topics configured%NC%
)

echo.
echo %GREEN%🎉 Firebase Setup Complete!%NC%
echo.
echo %BLUE%Next Steps:%NC%
echo 1. Go to Firebase Console: https://console.firebase.google.com/project/linkinblink-f544a
echo 2. Enable Authentication providers (Email/Password, Phone)
echo 3. Get FCM Server Key from Cloud Messaging settings
echo 4. Update mobile apps with Firebase configuration
echo.
echo %YELLOW%Test Credentials:%NC%
echo User: <EMAIL> / password123
echo Organizer: <EMAIL> / password123
echo.
pause
