{"ast": null, "code": "import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { differenceInMonths } from \"./differenceInMonths.js\";\n\n/**\n * The {@link differenceInQuarters} function options.\n */\n\n/**\n * @name differenceInQuarters\n * @category Quarter Helpers\n * @summary Get the number of quarters between the given dates.\n *\n * @description\n * Get the number of quarters between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of full quarters\n *\n * @example\n * // How many full quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInQuarters(new Date(2014, 6, 2), new Date(2013, 11, 31))\n * //=> 2\n */\nexport function differenceInQuarters(laterDate, earlierDate, options) {\n  const diff = differenceInMonths(laterDate, earlierDate, options) / 3;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInQuarters;", "map": {"version": 3, "names": ["getRoundingMethod", "differenceInMonths", "differenceInQuarters", "laterDate", "earlierDate", "options", "diff", "roundingMethod"], "sources": ["G:/linkinblink/hotel-dashboard/node_modules/date-fns/differenceInQuarters.js"], "sourcesContent": ["import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { differenceInMonths } from \"./differenceInMonths.js\";\n\n/**\n * The {@link differenceInQuarters} function options.\n */\n\n/**\n * @name differenceInQuarters\n * @category Quarter Helpers\n * @summary Get the number of quarters between the given dates.\n *\n * @description\n * Get the number of quarters between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of full quarters\n *\n * @example\n * // How many full quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInQuarters(new Date(2014, 6, 2), new Date(2013, 11, 31))\n * //=> 2\n */\nexport function differenceInQuarters(laterDate, earlierDate, options) {\n  const diff = differenceInMonths(laterDate, earlierDate, options) / 3;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInQuarters;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,kBAAkB,QAAQ,yBAAyB;;AAE5D;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACpE,MAAMC,IAAI,GAAGL,kBAAkB,CAACE,SAAS,EAAEC,WAAW,EAAEC,OAAO,CAAC,GAAG,CAAC;EACpE,OAAOL,iBAAiB,CAACK,OAAO,EAAEE,cAAc,CAAC,CAACD,IAAI,CAAC;AACzD;;AAEA;AACA,eAAeJ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}