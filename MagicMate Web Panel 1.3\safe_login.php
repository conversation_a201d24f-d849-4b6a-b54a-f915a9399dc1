<?php
session_start();

// Database connection
$login_error = "";
$login_success = false;

// Process login
if (isset($_POST['login_submit'])) {
    try {
        // Database connection
        $evmulti = new mysqli("localhost", "username", "password", "database");
        
        if ($evmulti->connect_error) {
            throw new Exception("Database connection failed: " . $evmulti->connect_error);
        }
        
        $username = mysqli_real_escape_string($evmulti, $_POST['username']);
        $password = mysqli_real_escape_string($evmulti, $_POST['password']);
        $stype = mysqli_real_escape_string($evmulti, $_POST['stype']);
        
        if ($stype == 'mowner') {
            $query = "SELECT * FROM admin WHERE username='$username' AND password='$password'";
        } else {
            $query = "SELECT * FROM tbl_sponsore WHERE email='$username' AND password='$password'";
        }
        
        $result = $evmulti->query($query);
        
        if ($result && $result->num_rows > 0) {
            $_SESSION["evename"] = $username;
            $_SESSION["stype"] = $stype;
            $login_success = true;
        } else {
            $login_error = "Invalid username or password!";
        }
        
        $evmulti->close();
        
    } catch (Exception $e) {
        $login_error = "Error: " . $e->getMessage();
        error_log("Login error: " . $e->getMessage());
    }
}

// Redirect if already logged in
if (isset($_SESSION["evename"]) && $login_success) {
    header("Location: dashboard.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Safe Login - Admin Panel</title>
    <link rel="stylesheet" type="text/css" href="assets/css/vendors/bootstrap.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 450px;
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h2 {
            color: #333;
            margin: 0;
            font-weight: 600;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-control {
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .form-control:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .btn-login {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .btn-login:hover {
            transform: translateY(-2px);
        }
        .alert {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #1565c0;
        }
        .links {
            text-align: center;
            margin-top: 20px;
        }
        .links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
        }
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h2>🔐 Admin Panel</h2>
            <p style="color: #666; margin: 5px 0;">Safe Login (No 500 Errors)</p>
        </div>
        
        <div class="test-info">
            <strong>🛡️ Error-Free Login</strong><br>
            This page handles all errors gracefully and won't show 500 errors.
        </div>
        
        <?php if ($login_error): ?>
            <div class="alert alert-danger">
                ❌ <?php echo htmlspecialchars($login_error); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($login_success): ?>
            <div class="alert alert-success">
                ✅ Login successful! Redirecting to dashboard...
                <script>
                    setTimeout(function() {
                        window.location.href = 'dashboard.php';
                    }, 2000);
                </script>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="form-group">
                <label for="username">Username/Email:</label>
                <input type="text" name="username" id="username" class="form-control" 
                       placeholder="Enter your username or email" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" name="password" id="password" class="form-control" 
                       placeholder="Enter your password" required>
            </div>
            
            <div class="form-group">
                <label for="stype">User Type:</label>
                <select name="stype" id="stype" class="form-control" required>
                    <option value="">Select User Type</option>
                    <option value="mowner">Master Admin</option>
                    <option value="sowner">Organizer Panel</option>
                </select>
            </div>
            
            <button type="submit" name="login_submit" class="btn-login">
                🔑 Sign In
            </button>
        </form>
        
        <div class="links">
            <a href="index.php">← Main Login</a>
            <a href="test_login_flow.php">🧪 Test Login</a>
            <a href="final_system_check.php">🔍 System Check</a>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-size: 12px; color: #666;">
            <strong>Debug Info:</strong><br>
            Server: <?php echo $_SERVER['SERVER_NAME']; ?><br>
            PHP Version: <?php echo PHP_VERSION; ?><br>
            Session Status: <?php echo session_status() == PHP_SESSION_ACTIVE ? 'Active' : 'Inactive'; ?><br>
            Current User: <?php echo isset($_SESSION["evename"]) ? $_SESSION["evename"] : 'Not logged in'; ?>
        </div>
    </div>
    
    <script>
        console.log("✅ Safe login page loaded without errors!");
        
        // Auto-focus username field
        document.getElementById('username').focus();
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const stype = document.getElementById('stype').value;
            
            if (!username || !password || !stype) {
                e.preventDefault();
                alert('Please fill in all fields!');
                return false;
            }
            
            console.log('Form submitted with:', {username, stype});
        });
    </script>
</body>
</html>
