// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI',
    appId: '1:408299360705:web:a9e5c8f4c2b8e9a9e5c8f4',
    messagingSenderId: '408299360705',
    projectId: 'linkinblink-f544a',
    authDomain: 'linkinblink-f544a.firebaseapp.com',
    storageBucket: 'linkinblink-f544a.appspot.com',
    measurementId: 'G-MEASUREMENT_ID',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI',
    appId: '1:408299360705:android:a9e5c8f4c2b8e9a9e5c8f4',
    messagingSenderId: '408299360705',
    projectId: 'linkinblink-f544a',
    storageBucket: 'linkinblink-f544a.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI',
    appId: '1:408299360705:ios:a9e5c8f4c2b8e9a9e5c8f4',
    messagingSenderId: '408299360705',
    projectId: 'linkinblink-f544a',
    storageBucket: 'linkinblink-f544a.appspot.com',
    iosBundleId: 'com.linkinblink.magicmate',
  );
}
