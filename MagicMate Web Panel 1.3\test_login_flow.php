<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Flow Test</title>
    <link rel="stylesheet" type="text/css" href="assets/css/vendors/bootstrap.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .status-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Login Flow Test</h1>
        
        <?php
        session_start();
        
        // Check if user is already logged in
        if (isset($_SESSION["evename"])) {
            echo '<div class="status status-success">
                ✅ <strong>Already Logged In!</strong><br>
                User: ' . $_SESSION["evename"] . '<br>
                Type: ' . $_SESSION["stype"] . '<br>
                <a href="dashboard.php" class="btn btn-success">Go to Dashboard</a>
                <a href="logout.php" class="btn btn-warning">Logout</a>
            </div>';
        }
        
        // Test database connection
        echo '<div class="test-section">
            <h3>1. Database Connection Test</h3>';
        
        try {
            $evmulti = new mysqli("localhost", "username", "password", "database");
            if ($evmulti->connect_error) {
                echo '<div class="status status-error">❌ Database connection failed: ' . $evmulti->connect_error . '</div>';
            } else {
                echo '<div class="status status-success">✅ Database connected successfully</div>';
                
                // Test admin table
                $admin_check = $evmulti->query("SELECT COUNT(*) as count FROM admin");
                if ($admin_check) {
                    $admin_count = $admin_check->fetch_assoc()['count'];
                    echo '<div class="status status-info">📊 Found ' . $admin_count . ' admin users in database</div>';

                    // Show first admin user (for testing)
                    $first_admin = $evmulti->query("SELECT username FROM admin LIMIT 1");
                    if ($first_admin && $first_admin->num_rows > 0) {
                        $admin_data = $first_admin->fetch_assoc();
                        echo '<div class="status status-info">👤 First admin username: <strong>' . $admin_data['username'] . '</strong></div>';
                    }
                } else {
                    echo '<div class="status status-error">❌ Cannot access admin table</div>';
                }
            }
        } catch (Exception $e) {
            echo '<div class="status status-error">❌ Database error: ' . $e->getMessage() . '</div>';
        }
        
        echo '</div>';
        
        // Process login if submitted
        if (isset($_POST['test_login'])) {
            echo '<div class="test-section">
                <h3>2. Login Test Result</h3>';
            
            $username = $_POST['username'];
            $password = $_POST['password'];
            $stype = $_POST['stype'];
            
            if ($stype == 'mowner') {
                $query = "SELECT * FROM admin WHERE username='$username' AND password='$password'";
            } else {
                $query = "SELECT * FROM tbl_sponsore WHERE email='$username' AND password='$password'";
            }
            
            $result = $evmulti->query($query);
            
            if ($result && $result->num_rows > 0) {
                $_SESSION["evename"] = $username;
                $_SESSION["stype"] = $stype;
                echo '<div class="status status-success">
                    ✅ <strong>Login Successful!</strong><br>
                    Username: ' . $username . '<br>
                    Type: ' . $stype . '<br>
                    <a href="dashboard.php" class="btn btn-success">Go to Dashboard</a>
                </div>';
            } else {
                echo '<div class="status status-error">
                    ❌ <strong>Login Failed!</strong><br>
                    Invalid username/password combination.<br>
                    Query: ' . $query . '
                </div>';
            }
            
            echo '</div>';
        }
        ?>
        
        <div class="test-section">
            <h3>3. Test Login Form</h3>
            <form method="POST" action="">
                <div style="margin-bottom: 15px;">
                    <label>Username:</label><br>
                    <input type="text" name="username" class="form-control" placeholder="Enter admin username" required>
                </div>
                <div style="margin-bottom: 15px;">
                    <label>Password:</label><br>
                    <input type="password" name="password" class="form-control" placeholder="Enter password" required>
                </div>
                <div style="margin-bottom: 15px;">
                    <label>Type:</label><br>
                    <select name="stype" class="form-control" required>
                        <option value="">Select Type</option>
                        <option value="mowner">Master Admin</option>
                        <option value="sowner">Organizer Panel</option>
                    </select>
                </div>
                <button type="submit" name="test_login" class="btn btn-primary">Test Login</button>
            </form>
        </div>
        
        <div class="test-section">
            <h3>4. Quick Actions</h3>
            <a href="index.php" class="btn btn-primary">Main Login Page</a>
            <a href="simple_login.php" class="btn btn-success">Simple Login</a>
            <a href="dashboard.php" class="btn btn-warning">Dashboard (Direct)</a>
            <a href="quick_fix_test.php" class="btn btn-danger">System Test</a>
        </div>
        
        <div class="test-section">
            <h3>5. Instructions</h3>
            <ol>
                <li><strong>Check database connection</strong> - Should show ✅</li>
                <li><strong>Note the admin username</strong> shown above</li>
                <li><strong>Try logging in</strong> with that username</li>
                <li><strong>If successful</strong> - click "Go to Dashboard"</li>
                <li><strong>If failed</strong> - check your database credentials</li>
            </ol>
        </div>
    </div>
</body>
</html>
