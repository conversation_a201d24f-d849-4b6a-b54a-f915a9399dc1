// ignore_for_file: prefer_const_constructors

import 'dart:io' show Platform;

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:magicmate/Api/data_store.dart';
import 'package:magicmate/helpar/routes_helpar.dart';
import 'package:magicmate/screen/language/local_string.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:magicmate/utils/Custom_widget.dart';
import 'package:provider/provider.dart';
import 'firebase/chat_page.dart';
import 'firebase/firebase_provider.dart';
import 'firebase_options.dart';
import 'helpar/get_di.dart' as di;

Future<void> _backgroundMessageHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase based on platform
  if (kIsWeb) {
    // For web platform
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  } else {
    // For mobile platforms
    try {
      if (Platform.isAndroid) {
        await Firebase.initializeApp();
      } else if (Platform.isIOS) {
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
      }
    } catch (e) {
      // Fallback initialization
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }
  }

  await di.init();
  await GetStorage.init();

  // Only initialize Firebase Messaging for non-web platforms
  if (!kIsWeb) {
    try {
      await FirebaseMessaging.instance.getInitialMessage();
      FirebaseMessaging.onBackgroundMessage(_backgroundMessageHandler);
    } catch (e) {
      print('Firebase Messaging not available on this platform: $e');
    }
  }

  initPlatformState();
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => FirebaseProvider()),
        ChangeNotifierProvider(create: (_) => UserProvider()),
      ],
      child: GetMaterialApp(
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          useMaterial3: false,
          primarySwatch: Colors.blue,
          fontFamily: "Gilroy",
        ),
        translations: LocaleString(),
        locale: getData.read("lan2") != null
            ? Locale(getData.read("lan2"), getData.read("lan1"))
            : Locale('en_US', 'en_US'),
        initialRoute: Routes.initial,
        getPages: getPages,
      ),
    ),
  );
}
