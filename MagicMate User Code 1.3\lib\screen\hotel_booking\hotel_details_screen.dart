import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:magicmate/utils/Colors.dart';
import 'package:magicmate/model/fontfamily_model.dart';
import 'package:magicmate/model/hotel_model.dart';
import 'package:magicmate/controller/hotel_controller.dart';
import 'package:magicmate/screen/hotel_booking/room_selection_screen.dart';

class HotelDetailsScreen extends StatefulWidget {
  final Hotel hotel;

  const HotelDetailsScreen({
    super.key,
    required this.hotel,
  });

  @override
  State<HotelDetailsScreen> createState() => _HotelDetailsScreenState();
}

class _HotelDetailsScreenState extends State<HotelDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final HotelController hotelController = Get.find<HotelController>();
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    hotelController.fetchRooms(widget.hotel.id);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: WhiteColor,
      body: CustomScrollView(
        slivers: [
          // App Bar with Images
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            backgroundColor: WhiteColor,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: WhiteColor),
              onPressed: () => Navigator.pop(context),
            ),
            actions: [
              IconButton(
                icon: Icon(
                  _isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: WhiteColor,
                ),
                onPressed: () {
                  setState(() {
                    _isFavorite = !_isFavorite;
                  });
                },
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // Hotel Image
                  widget.hotel.images.isNotEmpty
                      ? Image.network(
                          widget.hotel.images.first,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                            color: Colors.grey.shade300,
                            child: Icon(
                              Icons.hotel,
                              size: 100,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        )
                      : Container(
                          color: Colors.grey.shade300,
                          child: Icon(
                            Icons.hotel,
                            size: 100,
                            color: Colors.grey.shade600,
                          ),
                        ),
                  // Gradient overlay
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.7),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Hotel Name and Rating
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.hotel.name,
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: BlackColor,
                              ),
                            ),
                            SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(Icons.location_on,
                                    color: Colors.grey, size: 16),
                                SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    '${widget.hotel.address}, ${widget.hotel.city}',
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: gradient.defoultColor,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.star, color: WhiteColor, size: 16),
                            SizedBox(width: 4),
                            Text(
                              widget.hotel.rating.toStringAsFixed(1),
                              style: TextStyle(
                                color: WhiteColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 20),

                  // Price
                  Row(
                    children: [
                      Text(
                        '₹${widget.hotel.price.toStringAsFixed(0)}',
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: gradient.defoultColor,
                        ),
                      ),
                      Text(
                        ' / night',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 20),

                  // Tab Bar
                  TabBar(
                    controller: _tabController,
                    labelColor: gradient.defoultColor,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: gradient.defoultColor,
                    tabs: [
                      Tab(text: 'Overview'),
                      Tab(text: 'Rooms'),
                      Tab(text: 'Amenities'),
                      Tab(text: 'Reviews'),
                    ],
                  ),

                  SizedBox(height: 20),

                  // Tab Content
                  SizedBox(
                    height: 400,
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildOverviewTab(),
                        _buildRoomsTab(),
                        _buildAmenitiesTab(),
                        _buildReviewsTab(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: WhiteColor,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade300,
              blurRadius: 10,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RoomSelectionScreen(hotel: widget.hotel),
              ),
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: gradient.defoultColor,
            padding: EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Text(
            'Select Rooms',
            style: TextStyle(
              color: WhiteColor,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'About this hotel',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: BlackColor,
            ),
          ),
          SizedBox(height: 12),
          Text(
            widget.hotel.description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
          SizedBox(height: 20),
          if (widget.hotel.phone != null) ...[
            _buildInfoRow(Icons.phone, 'Phone', widget.hotel.phone!),
            SizedBox(height: 12),
          ],
          if (widget.hotel.email != null) ...[
            _buildInfoRow(Icons.email, 'Email', widget.hotel.email!),
            SizedBox(height: 12),
          ],
          if (widget.hotel.website != null) ...[
            _buildInfoRow(Icons.language, 'Website', widget.hotel.website!),
          ],
        ],
      ),
    );
  }

  Widget _buildRoomsTab() {
    return Obx(() {
      if (hotelController.isLoadingRooms.value) {
        return Center(child: CircularProgressIndicator());
      }

      if (hotelController.rooms.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.bed, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'No rooms available',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        itemCount: hotelController.rooms.length,
        itemBuilder: (context, index) {
          final room = hotelController.rooms[index];
          return Card(
            margin: EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    room.name,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    room.description,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                  SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '₹${room.price.toStringAsFixed(0)}/night',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: gradient.defoultColor,
                        ),
                      ),
                      Text(
                        'Capacity: ${room.capacity}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      );
    });
  }

  Widget _buildAmenitiesTab() {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: widget.hotel.amenities.length,
      itemBuilder: (context, index) {
        final amenity = widget.hotel.amenities[index];
        return Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                _getAmenityIcon(amenity),
                color: gradient.defoultColor,
                size: 20,
              ),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  amenity,
                  style: TextStyle(fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildReviewsTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.rate_review, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Reviews coming soon!',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, color: gradient.defoultColor, size: 20),
        SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: BlackColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  IconData _getAmenityIcon(String amenity) {
    switch (amenity.toLowerCase()) {
      case 'wifi':
      case 'wi-fi':
        return Icons.wifi;
      case 'parking':
        return Icons.local_parking;
      case 'pool':
      case 'swimming pool':
        return Icons.pool;
      case 'gym':
      case 'fitness center':
        return Icons.fitness_center;
      case 'restaurant':
        return Icons.restaurant;
      case 'spa':
        return Icons.spa;
      case 'bar':
        return Icons.local_bar;
      case 'room service':
        return Icons.room_service;
      case 'laundry':
        return Icons.local_laundry_service;
      case 'air conditioning':
      case 'ac':
        return Icons.ac_unit;
      default:
        return Icons.check_circle;
    }
  }
}
