<?php
/**
 * Firebase Cloud Messaging (FCM) Notification Service
 * Sends push notifications to mobile apps
 */

class FirebaseNotificationService {
    
    private $serverKey;
    private $projectId;
    private $apiUrl;
    
    public function __construct() {
        // Firebase project configuration
        $this->projectId = 'linkinblink-f544a';
        $this->serverKey = 'YOUR_FCM_SERVER_KEY_HERE'; // Get from Firebase Console
        $this->apiUrl = "https://fcm.googleapis.com/v1/projects/{$this->projectId}/messages:send";
    }
    
    /**
     * Send notification to a single device
     */
    public function sendToDevice($fcmToken, $title, $body, $data = []) {
        $message = [
            'message' => [
                'token' => $fcmToken,
                'notification' => [
                    'title' => $title,
                    'body' => $body
                ],
                'data' => $data,
                'android' => [
                    'notification' => [
                        'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                        'sound' => 'default'
                    ]
                ],
                'apns' => [
                    'payload' => [
                        'aps' => [
                            'sound' => 'default'
                        ]
                    ]
                ]
            ]
        ];
        
        return $this->sendNotification($message);
    }
    
    /**
     * Send notification to multiple devices
     */
    public function sendToMultipleDevices($fcmTokens, $title, $body, $data = []) {
        $results = [];
        foreach ($fcmTokens as $token) {
            $results[] = $this->sendToDevice($token, $title, $body, $data);
        }
        return $results;
    }
    
    /**
     * Send notification to a topic
     */
    public function sendToTopic($topic, $title, $body, $data = []) {
        $message = [
            'message' => [
                'topic' => $topic,
                'notification' => [
                    'title' => $title,
                    'body' => $body
                ],
                'data' => $data
            ]
        ];
        
        return $this->sendNotification($message);
    }
    
    /**
     * Send event booking confirmation
     */
    public function sendEventBookingConfirmation($fcmToken, $eventTitle, $bookingId) {
        $title = "Booking Confirmed!";
        $body = "Your booking for '{$eventTitle}' has been confirmed.";
        $data = [
            'type' => 'event_booking',
            'booking_id' => $bookingId,
            'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
        ];
        
        return $this->sendToDevice($fcmToken, $title, $body, $data);
    }
    
    /**
     * Send hotel booking confirmation
     */
    public function sendHotelBookingConfirmation($fcmToken, $hotelName, $bookingId) {
        $title = "Hotel Booking Confirmed!";
        $body = "Your booking at '{$hotelName}' has been confirmed.";
        $data = [
            'type' => 'hotel_booking',
            'booking_id' => $bookingId,
            'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
        ];
        
        return $this->sendToDevice($fcmToken, $title, $body, $data);
    }
    
    /**
     * Send chat message notification
     */
    public function sendChatNotification($fcmToken, $senderName, $message) {
        $title = "New Message from {$senderName}";
        $body = $message;
        $data = [
            'type' => 'chat_message',
            'sender_name' => $senderName,
            'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
        ];
        
        return $this->sendToDevice($fcmToken, $title, $body, $data);
    }
    
    /**
     * Send event reminder
     */
    public function sendEventReminder($fcmToken, $eventTitle, $eventDate) {
        $title = "Event Reminder";
        $body = "Don't forget! '{$eventTitle}' is tomorrow at {$eventDate}.";
        $data = [
            'type' => 'event_reminder',
            'event_title' => $eventTitle,
            'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
        ];
        
        return $this->sendToDevice($fcmToken, $title, $body, $data);
    }
    
    /**
     * Send payment confirmation
     */
    public function sendPaymentConfirmation($fcmToken, $amount, $transactionId) {
        $title = "Payment Successful";
        $body = "Your payment of ₹{$amount} has been processed successfully.";
        $data = [
            'type' => 'payment_confirmation',
            'amount' => $amount,
            'transaction_id' => $transactionId,
            'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
        ];
        
        return $this->sendToDevice($fcmToken, $title, $body, $data);
    }
    
    /**
     * Send general announcement
     */
    public function sendAnnouncement($topic, $title, $body) {
        $data = [
            'type' => 'announcement',
            'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
        ];
        
        return $this->sendToTopic($topic, $title, $body, $data);
    }
    
    /**
     * Core notification sending function
     */
    private function sendNotification($message) {
        try {
            // Get access token (you'll need to implement OAuth2 for production)
            $accessToken = $this->getAccessToken();
            
            $headers = [
                'Authorization: Bearer ' . $accessToken,
                'Content-Type: application/json'
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            $result = json_decode($response, true);
            
            if ($httpCode == 200) {
                return [
                    'success' => true,
                    'message' => 'Notification sent successfully',
                    'response' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to send notification',
                    'error' => $result,
                    'http_code' => $httpCode
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Exception occurred',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get OAuth2 access token for FCM v1 API
     * For production, implement proper OAuth2 flow
     */
    private function getAccessToken() {
        // For now, return the server key (legacy method)
        // In production, implement OAuth2 with service account
        return $this->serverKey;
    }
    
    /**
     * Subscribe device to topic
     */
    public function subscribeToTopic($fcmToken, $topic) {
        $url = "https://iid.googleapis.com/iid/v1/{$fcmToken}/rel/topics/{$topic}";
        
        $headers = [
            'Authorization: key=' . $this->serverKey,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return $httpCode == 200;
    }
    
    /**
     * Unsubscribe device from topic
     */
    public function unsubscribeFromTopic($fcmToken, $topic) {
        $url = "https://iid.googleapis.com/iid/v1/{$fcmToken}/rel/topics/{$topic}";
        
        $headers = [
            'Authorization: key=' . $this->serverKey,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return $httpCode == 200;
    }
}

// Usage Examples:
/*
// Initialize service
$fcmService = new FirebaseNotificationService();

// Send event booking confirmation
$fcmService->sendEventBookingConfirmation(
    'user_fcm_token_here',
    'Music Concert 2024',
    'booking_123'
);

// Send to multiple users
$tokens = ['token1', 'token2', 'token3'];
$fcmService->sendToMultipleDevices(
    $tokens,
    'New Event Available',
    'Check out the latest events in your area!'
);

// Send announcement to all users
$fcmService->sendAnnouncement(
    'all_users',
    'App Update',
    'New features are now available!'
);
*/
?>
