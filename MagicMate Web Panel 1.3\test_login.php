<?php
session_start();

// Test page to verify domain validation is removed
echo "<h1>Domain Validation Test</h1>";
echo "<p>Testing if domain validation has been successfully removed...</p>";

// Check if session works
if (isset($_SESSION["evename"])) {
    echo "<p style='color: green;'>✅ Session is active for user: " . $_SESSION["evename"] . "</p>";
    echo "<p><a href='dashboard.php'>Go to Dashboard</a></p>";
} else {
    echo "<p style='color: orange;'>⚠️ No active session found</p>";
    echo "<p><a href='index.php'>Go to Login</a></p>";
}

// Check if domain validation files exist
if (file_exists('validate_domain.php')) {
    echo "<p style='color: red;'>❌ validate_domain.php still exists</p>";
} else {
    echo "<p style='color: green;'>✅ validate_domain.php has been removed</p>";
}

// Check script.php content
$scriptContent = file_get_contents('filemanager/script.php');
if (strpos($scriptContent, 'maindata') !== false) {
    echo "<p style='color: red;'>❌ Domain validation scripts still present in script.php</p>";
} else {
    echo "<p style='color: green;'>✅ Domain validation scripts removed from script.php</p>";
}

// Check evconfing.php content
$configContent = file_get_contents('filemanager/evconfing.php');
if (strpos($configContent, 'tbl_etom') !== false && strpos($configContent, '//') === false) {
    echo "<p style='color: red;'>❌ Domain validation query still active in evconfing.php</p>";
} else {
    echo "<p style='color: green;'>✅ Domain validation query disabled in evconfing.php</p>";
}

echo "<hr>";
echo "<h2>Test Results Summary:</h2>";
echo "<p>If all items above show ✅, then domain validation has been successfully removed!</p>";
echo "<p>You can now login directly and access the dashboard without any domain validation.</p>";

// Test database connection
try {
    include 'filemanager/evconfing.php';
    echo "<p style='color: green;'>✅ Database connection working</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection issue: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Domain Validation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        p { margin: 10px 0; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <!-- Content already echoed above -->
</body>
</html>
