// Firestore Collections Setup Script
// Run with: node create_firestore_collections.js

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
const serviceAccount = ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************;

// Initialize Firebase (comment out if using default credentials)
// admin.initializeApp({
//   credential: admin.credential.cert(serviceAccount)
// });

// For local development, use default credentials
admin.initializeApp();

const db = admin.firestore();

async function createCollections() {
  console.log('🔥 Creating Firestore Collections for MagicMate...');
  
  try {
    // 1. Create MagicUser collection
    console.log('📱 Creating MagicUser collection...');
    await db.collection('MagicUser').doc('1').set({
      uid: '1',
      name: 'Test User',
      email: '<EMAIL>',
      mobile: '+************',
      profilePic: '',
      fcmToken: 'sample_fcm_token',
      isOnline: true,
      lastActive: admin.firestore.FieldValue.serverTimestamp(),
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      appType: 'event_user'
    });
    console.log('✅ MagicUser collection created');

    // 2. Create MagicOrganizer collection
    console.log('🏢 Creating MagicOrganizer collection...');
    await db.collection('MagicOrganizer').doc('1').set({
      uid: '1',
      name: 'Test Organizer',
      email: '<EMAIL>',
      mobile: '+************',
      profilePic: '',
      fcmToken: 'sample_fcm_token',
      isOnline: false,
      lastActive: admin.firestore.FieldValue.serverTimestamp(),
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      appType: 'event_organizer'
    });
    console.log('✅ MagicOrganizer collection created');

    // 3. Create Magic_Organization_rooms collection
    console.log('💬 Creating Magic_Organization_rooms collection...');
    await db.collection('Magic_Organization_rooms').doc('U1_O1').set({
      uid: 'U1_O1',
      name: 'Test User',
      email: '<EMAIL>',
      image: '',
      isOnline: true,
      lastActive: admin.firestore.FieldValue.serverTimestamp(),
      participants: ['1', '1'],
      lastMessage: 'Hello!',
      lastMessageTime: admin.firestore.FieldValue.serverTimestamp()
    });

    // Add sample message to subcollection
    await db.collection('Magic_Organization_rooms').doc('U1_O1')
      .collection('messages').add({
        content: 'Hello, I need help with event booking',
        senderId: '1',
        receiverId: '1',
        sentTime: admin.firestore.FieldValue.serverTimestamp(),
        messageType: 'text',
        userType: 'US'
      });
    console.log('✅ Magic_Organization_rooms collection created');

    // 4. Create events collection
    console.log('🎉 Creating events collection...');
    await db.collection('events').doc('1').set({
      eventId: '1',
      title: 'Sample Event',
      description: 'This is a sample event',
      organizerId: '1',
      startDate: admin.firestore.Timestamp.fromDate(new Date('2025-01-15T18:00:00Z')),
      endDate: admin.firestore.Timestamp.fromDate(new Date('2025-01-15T22:00:00Z')),
      location: 'Mumbai, India',
      price: 100,
      availableTickets: 50,
      totalTickets: 100,
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    console.log('✅ events collection created');

    // 5. Create bookings collection
    console.log('🎫 Creating bookings collection...');
    await db.collection('bookings').doc('1').set({
      bookingId: '1',
      userId: '1',
      eventId: '1',
      organizerId: '1',
      ticketCount: 2,
      totalAmount: 200,
      bookingStatus: 'confirmed',
      paymentStatus: 'paid',
      bookingDate: admin.firestore.FieldValue.serverTimestamp(),
      eventDate: admin.firestore.Timestamp.fromDate(new Date('2025-01-15T18:00:00Z'))
    });
    console.log('✅ bookings collection created');

    // 6. Create hotels collection
    console.log('🏨 Creating hotels collection...');
    await db.collection('hotels').doc('1').set({
      hotelId: '1',
      name: 'Sample Hotel',
      description: 'A beautiful hotel in Mumbai',
      location: 'Mumbai, India',
      rating: 4.5,
      amenities: ['wifi', 'parking', 'pool', 'restaurant'],
      images: ['https://example.com/hotel1.jpg'],
      pricePerNight: 2000,
      availableRooms: 10,
      totalRooms: 20,
      status: 'active'
    });
    console.log('✅ hotels collection created');

    // 7. Create hotel_bookings collection
    console.log('🏨 Creating hotel_bookings collection...');
    await db.collection('hotel_bookings').doc('1').set({
      bookingId: '1',
      userId: '1',
      hotelId: '1',
      checkInDate: admin.firestore.Timestamp.fromDate(new Date('2025-01-15T14:00:00Z')),
      checkOutDate: admin.firestore.Timestamp.fromDate(new Date('2025-01-17T11:00:00Z')),
      roomCount: 1,
      guestCount: 2,
      totalAmount: 4000,
      bookingStatus: 'confirmed',
      paymentStatus: 'paid'
    });
    console.log('✅ hotel_bookings collection created');

    // 8. Create notifications collection
    console.log('🔔 Creating notifications collection...');
    await db.collection('notifications').add({
      userId: '1',
      title: 'Booking Confirmed',
      body: 'Your event booking has been confirmed',
      type: 'event_booking',
      data: {
        eventId: '1',
        bookingId: '1'
      },
      sentAt: admin.firestore.FieldValue.serverTimestamp(),
      readAt: null,
      status: 'sent'
    });
    console.log('✅ notifications collection created');

    console.log('');
    console.log('🎉 All Firestore collections created successfully!');
    console.log('');
    console.log('📊 Collections created:');
    console.log('✅ MagicUser');
    console.log('✅ MagicOrganizer');
    console.log('✅ Magic_Organization_rooms (with messages subcollection)');
    console.log('✅ events');
    console.log('✅ bookings');
    console.log('✅ hotels');
    console.log('✅ hotel_bookings');
    console.log('✅ notifications');
    console.log('');
    console.log('🔗 View in Firebase Console:');
    console.log('https://console.firebase.google.com/project/linkinblink-f544a/firestore');

  } catch (error) {
    console.error('❌ Error creating collections:', error);
  }
}

// Run the setup
createCollections().then(() => {
  console.log('✅ Setup complete!');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Setup failed:', error);
  process.exit(1);
});

// Export for use in other scripts
module.exports = { createCollections };
