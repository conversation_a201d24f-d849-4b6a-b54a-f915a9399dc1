<?php
// Quick test to check if the admin panel is working
session_start();

echo "<h1>🔧 Admin Panel Quick Fix Test</h1>";

// Test 1: Database Connection
echo "<h2>1. Database Connection Test:</h2>";
try {
    $evmulti = new mysqli("localhost", "username", "password", "database");
    if ($evmulti->connect_error) {
        echo "<p style='color: red;'>❌ Database connection failed: " . $evmulti->connect_error . "</p>";
    } else {
        echo "<p style='color: green;'>✅ Database connection successful</p>";
        
        // Test settings table
        $result = $evmulti->query("SELECT * FROM tbl_setting LIMIT 1");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✅ Settings table accessible</p>";
        } else {
            echo "<p style='color: red;'>❌ Settings table not accessible</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test 2: Session Check
echo "<h2>2. Session Test:</h2>";
if (isset($_SESSION["evename"])) {
    echo "<p style='color: green;'>✅ Admin session active: " . $_SESSION["evename"] . "</p>";
    echo "<p><a href='dashboard.php' style='color: blue;'>→ Go to Dashboard</a></p>";
} else {
    echo "<p style='color: orange;'>⚠️ No admin session found</p>";
    echo "<p><a href='index.php' style='color: blue;'>→ Go to Login</a></p>";
}

// Test 3: File Structure
echo "<h2>3. File Structure Test:</h2>";
$required_files = [
    'index.php' => 'Login page',
    'dashboard.php' => 'Dashboard',
    'setting.php' => 'Settings',
    'filemanager/head.php' => 'Header file',
    'filemanager/script.php' => 'Scripts file',
    'filemanager/evconfing.php' => 'Configuration'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $description ($file)</p>";
    } else {
        echo "<p style='color: red;'>❌ Missing: $description ($file)</p>";
    }
}

// Test 4: JavaScript/CSS Assets
echo "<h2>4. Assets Test:</h2>";
$asset_dirs = [
    'assets/js' => 'JavaScript files',
    'assets/css' => 'CSS files',
    'images' => 'Images directory'
];

foreach ($asset_dirs as $dir => $description) {
    if (is_dir($dir)) {
        echo "<p style='color: green;'>✅ $description ($dir)</p>";
    } else {
        echo "<p style='color: red;'>❌ Missing: $description ($dir)</p>";
    }
}

// Test 5: Quick Login Form (for testing)
if (!isset($_SESSION["evename"])) {
    echo "<h2>5. Quick Login Test:</h2>";
    echo "<form method='POST' style='border: 1px solid #ccc; padding: 20px; margin: 10px 0;'>";
    echo "<p><strong>Test Admin Login:</strong></p>";
    echo "<p>Username: <input type='text' name='test_user' placeholder='admin' style='margin-left: 10px;'></p>";
    echo "<p>Password: <input type='password' name='test_pass' placeholder='password' style='margin-left: 10px;'></p>";
    echo "<p><input type='submit' name='test_login' value='Test Login' style='background: #007bff; color: white; padding: 5px 15px; border: none;'></p>";
    echo "</form>";
    
    if (isset($_POST['test_login'])) {
        // This is just for testing - replace with your actual admin credentials
        if ($_POST['test_user'] == 'admin' && $_POST['test_pass'] == 'admin') {
            $_SESSION["evename"] = $_POST['test_user'];
            echo "<p style='color: green;'>✅ Test login successful! <a href='dashboard.php'>Go to Dashboard</a></p>";
        } else {
            echo "<p style='color: red;'>❌ Test login failed. Use your actual admin credentials.</p>";
        }
    }
}

echo "<hr>";
echo "<h2>🎯 Summary:</h2>";
echo "<p>If all tests show ✅, your admin panel should be working properly.</p>";
echo "<p>If you see ❌, those issues need to be fixed.</p>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>1. Fix any ❌ issues shown above</li>";
echo "<li>2. Try logging in with your actual admin credentials</li>";
echo "<li>3. Check if dashboard loads properly</li>";
echo "<li>4. Delete this test file after verification</li>";
echo "</ul>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Admin Panel Quick Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1, h2 { color: #333; }
        p { margin: 8px 0; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
        ul { margin: 10px 0; }
        li { margin: 5px 0; }
    </style>
</head>
<body>
    <!-- Content already echoed above -->
</body>
</html>
