<?php
// Essential scripts for admin panel functionality
?>
<script src="assets/js/jquery-3.5.1.min.js"></script>
<script src="assets/js/bootstrap/bootstrap.bundle.min.js"></script>
<script src="assets/js/icons/feather-icon/feather.min.js"></script>
<script src="assets/js/icons/feather-icon/feather-icon.js"></script>
<script src="assets/js/scrollbar/simplebar.js"></script>
<script src="assets/js/scrollbar/custom.js"></script>
<script src="assets/js/config.js"></script>
<script src="assets/js/sidebar-menu.js"></script>
<script src="assets/js/script.js"></script>
<script src="assets/js/datatable/datatables/jquery.dataTables.min.js"></script>
<script src="assets/js/datatable/datatables/datatable.custom.js"></script>
<script src="assets/summernote/summernote-bs4.min.js"></script>
<script src="assets/bootstrap-tagsinput/bootstrap-tagsinput.min.js"></script>

<!-- CRITICAL: Remove loader immediately -->
<script>
// Remove loader as soon as possible
$(window).on('load', function() {
    $('.loader-wrapper').fadeOut(500);
});

// Backup: Remove loader after 2 seconds maximum
setTimeout(function() {
    $('.loader-wrapper').fadeOut(500);
}, 2000);

// Emergency: Remove loader after 5 seconds no matter what
setTimeout(function() {
    $('.loader-wrapper').remove();
}, 5000);

$(document).ready(function() {
    // Remove loader when document is ready
    $('.loader-wrapper').fadeOut(500);

    // Initialize DataTables if present
    if ($('.display').length) {
        $('.display').DataTable();
    }

    // Initialize Summernote if present
    if ($('.summernote').length) {
        $('.summernote').summernote({
            height: 200
        });
    }

    // Initialize tooltips if present
    if ($('[data-toggle="tooltip"]').length) {
        $('[data-toggle="tooltip"]').tooltip();
    }
});
</script>
