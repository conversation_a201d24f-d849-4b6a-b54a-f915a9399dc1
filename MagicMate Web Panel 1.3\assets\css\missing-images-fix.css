/* 
 * Missing Images Fix CSS
 * This file fixes 404 errors for missing background images
 * Include this after the main style.css
 */

/* Fix for missing dashboard background images */
.social-profile {
    background-image: none !important;
    background-color: #f8f9fa !important;
}

.balance-widget {
    background-image: none !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.welcome-card {
    background-image: none !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.balance-box {
    background-image: none !important;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.widget-1 {
    background-image: none !important;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

.error-wrapper.maintenance-bg {
    background-image: none !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.page-wrapper.material-type .page-body-wrapper {
    background-image: none !important;
    background-color: #f8f9fa !important;
}

.box-layout {
    background-image: none !important;
    background-color: #f8f9fa !important;
}

.landing-home {
    background-image: none !important;
    background: linear-gradient(135deg, #1b1c21 0%, #2c3e50 100%) !important;
}

/* Ensure login page works properly */
.login-card {
    background: url("../images/bg.jpg") !important;
    background-position: center !important;
    background-size: cover !important;
}

/* Alternative: If bg.jpg also doesn't work, use gradient */
.login-card-fallback {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* Fix for any other missing images - hide them gracefully */
[style*="background-image"]:not([style*="data:"]) {
    background-image: none !important;
}

/* Add some nice gradients for visual appeal */
.card {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
    border: none !important;
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%) !important;
    border: none !important;
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    border: none !important;
}

.btn-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    border: none !important;
}

/* Ensure text is readable on gradients */
.btn {
    color: white !important;
    font-weight: 500 !important;
}

/* Fix for loader if it's still showing */
.loader-wrapper {
    transition: opacity 0.3s ease-out !important;
}

/* Hide any broken images */
img[src=""], img:not([src]), img[src*="404"] {
    display: none !important;
}

/* Fallback for broken images */
img {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

/* Ensure forms work properly */
.form-control:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
}

/* Make sure tables look good */
.table {
    background-color: white !important;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02) !important;
}

/* Ensure sidebar works */
.sidebar-wrapper {
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%) !important;
}

/* Fix navbar */
.page-header {
    background: white !important;
    border-bottom: 1px solid #e9ecef !important;
}

/* Ensure dashboard cards are visible */
.dashboard-card {
    background: white !important;
    border: 1px solid #e9ecef !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Make sure content is readable */
.page-body {
    background-color: #f8f9fa !important;
    min-height: 100vh !important;
}

/* Fix any modal issues */
.modal-content {
    background: white !important;
    border: 1px solid #e9ecef !important;
}

/* Ensure dropdowns work */
.dropdown-menu {
    background: white !important;
    border: 1px solid #e9ecef !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

/* Fix alerts */
.alert {
    border: 1px solid transparent !important;
    border-radius: 4px !important;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
    border-color: #c3e6cb !important;
    color: #155724 !important;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
    border-color: #f5c6cb !important;
    color: #721c24 !important;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
    border-color: #ffeaa7 !important;
    color: #856404 !important;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%) !important;
    border-color: #bee5eb !important;
    color: #0c5460 !important;
}
