{"name": "magicmate-firebase-setup", "version": "1.0.0", "description": "Complete Firebase setup for MagicMate event and hotel booking app", "main": "index.js", "scripts": {"setup": "node setup_firebase.js", "create-collections": "node create_firestore_collections.js", "create-test-users": "node create_test_users.js", "test-auth": "node test_auth.js", "test-notifications": "node test_notifications.js", "setup-topics": "node manage_topics.js", "test-fcm": "npm run test-notifications && npm run setup-topics", "deploy-rules": "firebase deploy --only firestore:rules,storage:rules", "deploy-all": "firebase deploy", "start": "npm run create-collections && npm run create-test-users && npm run setup-topics"}, "keywords": ["firebase", "firestore", "fcm", "authentication", "magicmate", "event-booking", "hotel-booking"], "author": "MagicMate Team", "license": "MIT", "dependencies": {"firebase-admin": "^12.0.0"}, "devDependencies": {}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-repo/magicmate-firebase"}, "bugs": {"url": "https://github.com/your-repo/magicmate-firebase/issues"}, "homepage": "https://github.com/your-repo/magicmate-firebase#readme"}