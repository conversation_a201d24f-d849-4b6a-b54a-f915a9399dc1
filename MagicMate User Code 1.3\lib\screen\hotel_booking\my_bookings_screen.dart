import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:magicmate/utils/Colors.dart';
import 'package:magicmate/model/fontfamily_model.dart';
import 'package:magicmate/model/booking_model.dart';
import 'package:magicmate/controller/hotel_controller.dart';
import 'package:magicmate/controller/login_controller.dart';
import 'package:magicmate/Api/data_store.dart';
import 'package:intl/intl.dart';

class MyBookingsScreen extends StatefulWidget {
  const MyBookingsScreen({super.key});

  @override
  State<MyBookingsScreen> createState() => _MyBookingsScreenState();
}

class _MyBookingsScreenState extends State<MyBookingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final HotelController hotelController = Get.find<HotelController>();
  final LoginController loginController = Get.find<LoginController>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _fetchBookings();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _fetchBookings() {
    var userData = getData.read("UserLogin");
    final userId = userData?['uid'];
    if (userId != null) {
      hotelController.fetchUserBookings(userId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: WhiteColor,
      appBar: AppBar(
        backgroundColor: WhiteColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: BlackColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'My Bookings',
          style: TextStyle(
            color: BlackColor,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          labelColor: gradient.defoultColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: gradient.defoultColor,
          tabs: [
            Tab(text: 'Upcoming'),
            Tab(text: 'Past'),
            Tab(text: 'Cancelled'),
          ],
        ),
      ),
      body: Obx(() {
        if (hotelController.isLoadingBookings.value) {
          return Center(child: CircularProgressIndicator());
        }

        return TabBarView(
          controller: _tabController,
          children: [
            _buildBookingsList(_getUpcomingBookings()),
            _buildBookingsList(_getPastBookings()),
            _buildBookingsList(_getCancelledBookings()),
          ],
        );
      }),
    );
  }

  List<Booking> _getUpcomingBookings() {
    final now = DateTime.now();
    return hotelController.userBookings
        .where((booking) =>
            booking.checkIn.isAfter(now) && booking.status != 'cancelled')
        .toList();
  }

  List<Booking> _getPastBookings() {
    final now = DateTime.now();
    return hotelController.userBookings
        .where((booking) =>
            booking.checkOut.isBefore(now) && booking.status != 'cancelled')
        .toList();
  }

  List<Booking> _getCancelledBookings() {
    return hotelController.userBookings
        .where((booking) => booking.status == 'cancelled')
        .toList();
  }

  Widget _buildBookingsList(List<Booking> bookings) {
    if (bookings.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_today,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No bookings found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Your bookings will appear here',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async => _fetchBookings(),
      child: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: bookings.length,
        itemBuilder: (context, index) {
          final booking = bookings[index];
          return _buildBookingCard(booking);
        },
      ),
    );
  }

  Widget _buildBookingCard(Booking booking) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with booking ID and status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Booking #${booking.id.substring(0, 8).toUpperCase()}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: gradient.defoultColor,
                  ),
                ),
                _buildStatusChip(booking.status),
              ],
            ),

            SizedBox(height: 12),

            // Hotel and Room info
            Text(
              booking.hotelName,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),

            SizedBox(height: 4),

            Text(
              booking.roomName,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),

            SizedBox(height: 16),

            // Dates and guests
            Row(
              children: [
                Expanded(
                  child: _buildInfoColumn(
                    Icons.calendar_today,
                    'Check-in',
                    DateFormat('MMM dd, yyyy').format(booking.checkIn),
                  ),
                ),
                Expanded(
                  child: _buildInfoColumn(
                    Icons.calendar_today,
                    'Check-out',
                    DateFormat('MMM dd, yyyy').format(booking.checkOut),
                  ),
                ),
                Expanded(
                  child: _buildInfoColumn(
                    Icons.people,
                    'Guests',
                    booking.guests.toString(),
                  ),
                ),
              ],
            ),

            SizedBox(height: 16),

            // Price and actions
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Total Amount',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      '₹${booking.totalPrice.toStringAsFixed(0)}',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: gradient.defoultColor,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    if (booking.canCheckIn) ...[
                      ElevatedButton(
                        onPressed: () => _checkIn(booking),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          padding:
                              EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Check In',
                          style: TextStyle(
                            color: WhiteColor,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      SizedBox(width: 8),
                    ],
                    if (booking.canCheckOut) ...[
                      ElevatedButton(
                        onPressed: () => _checkOut(booking),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          padding:
                              EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Check Out',
                          style: TextStyle(
                            color: WhiteColor,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      SizedBox(width: 8),
                    ],
                    OutlinedButton(
                      onPressed: () => _showBookingDetails(booking),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: gradient.defoultColor),
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Details',
                        style: TextStyle(
                          color: gradient.defoultColor,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color backgroundColor;
    Color textColor;

    switch (status.toLowerCase()) {
      case 'confirmed':
        backgroundColor = Colors.green.shade50;
        textColor = Colors.green;
        break;
      case 'pending':
        backgroundColor = Colors.orange.shade50;
        textColor = Colors.orange;
        break;
      case 'cancelled':
        backgroundColor = Colors.red.shade50;
        textColor = Colors.red;
        break;
      default:
        backgroundColor = Colors.grey.shade50;
        textColor = Colors.grey;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.toUpperCase(),
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: textColor,
        ),
      ),
    );
  }

  Widget _buildInfoColumn(IconData icon, String label, String value) {
    return Column(
      children: [
        Icon(icon, size: 16, color: Colors.grey.shade600),
        SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey.shade600,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  void _checkIn(Booking booking) {
    // Implement check-in logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Check-in functionality coming soon!'),
        backgroundColor: gradient.defoultColor,
      ),
    );
  }

  void _checkOut(Booking booking) {
    // Implement check-out logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Check-out functionality coming soon!'),
        backgroundColor: gradient.defoultColor,
      ),
    );
  }

  void _showBookingDetails(Booking booking) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Booking Details',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20),
            _buildDetailRow(
                'Booking ID', booking.id.substring(0, 8).toUpperCase()),
            _buildDetailRow('Hotel', booking.hotelName),
            _buildDetailRow('Room', booking.roomName),
            _buildDetailRow(
                'Check-in', DateFormat('MMM dd, yyyy').format(booking.checkIn)),
            _buildDetailRow('Check-out',
                DateFormat('MMM dd, yyyy').format(booking.checkOut)),
            _buildDetailRow('Guests', booking.guests.toString()),
            _buildDetailRow('Nights', booking.nights.toString()),
            _buildDetailRow(
                'Total Amount', '₹${booking.totalPrice.toStringAsFixed(0)}'),
            _buildDetailRow('Status', booking.status.toUpperCase()),
            _buildDetailRow(
                'Payment Status', booking.paymentStatus.toUpperCase()),
            if (booking.guestDetails != null) ...[
              SizedBox(height: 16),
              Text(
                'Guest Information',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              _buildDetailRow('Name', booking.guestDetails!['name'] ?? ''),
              _buildDetailRow('Email', booking.guestDetails!['email'] ?? ''),
              _buildDetailRow('Phone', booking.guestDetails!['phone'] ?? ''),
            ],
            Spacer(),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: gradient.defoultColor,
                padding: EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'Close',
                style: TextStyle(
                  color: WhiteColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
