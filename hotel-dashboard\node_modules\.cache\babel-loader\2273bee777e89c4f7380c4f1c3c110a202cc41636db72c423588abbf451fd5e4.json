{"ast": null, "code": "var _jsxFileName = \"G:\\\\linkinblink\\\\hotel-dashboard\\\\src\\\\pages\\\\admin\\\\Staff.tsx\";\nimport React from 'react';\nimport { Box, Typography, Paper } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Staff = () => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Staff Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: \"Staff management page content will go here.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Staff;\nexport default Staff;\nvar _c;\n$RefreshReg$(_c, \"Staff\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Paper", "jsxDEV", "_jsxDEV", "Staff", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "_c", "$RefreshReg$"], "sources": ["G:/linkinblink/hotel-dashboard/src/pages/admin/Staff.tsx"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, Paper } from '@mui/material';\n\nconst Staff: React.FC = () => {\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        Staff Management\n      </Typography>\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"body1\">\n          Staff management page content will go here.\n        </Typography>\n      </Paper>\n    </Box>\n  );\n};\n\nexport default Staff;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,KAAK,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAC5B,oBACED,OAAA,CAACJ,GAAG;IAAAM,QAAA,gBACFF,OAAA,CAACH,UAAU;MAACM,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbR,OAAA,CAACF,KAAK;MAACW,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,eAClBF,OAAA,CAACH,UAAU;QAACM,OAAO,EAAC,OAAO;QAAAD,QAAA,EAAC;MAE5B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACG,EAAA,GAbIV,KAAe;AAerB,eAAeA,KAAK;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}