{"ast": null, "code": "export { default } from \"./GridLegacy.js\";\nexport { default as gridLegacyClasses } from \"./gridLegacyClasses.js\";\nexport * from \"./gridLegacyClasses.js\";", "map": {"version": 3, "names": ["default", "gridLegacyClasses"], "sources": ["G:/linkinblink/hotel-dashboard/node_modules/@mui/material/esm/GridLegacy/index.js"], "sourcesContent": ["export { default } from \"./GridLegacy.js\";\nexport { default as gridLegacyClasses } from \"./gridLegacyClasses.js\";\nexport * from \"./gridLegacyClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB;AACzC,SAASA,OAAO,IAAIC,iBAAiB,QAAQ,wBAAwB;AACrE,cAAc,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}