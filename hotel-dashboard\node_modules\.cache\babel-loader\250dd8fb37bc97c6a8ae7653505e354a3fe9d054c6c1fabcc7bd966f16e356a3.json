{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"m21 15.56-4.24-1.89H15V7.5c0-.28-.22-.5-.5-.5s-.5.22-.5.5v10.61l-4.17-.89 3.7 3.78h6.55z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M6 2.5V1h5v5H9.5V3.56L3.56 9.5H6V11H1V6h1.5v2.44L8.44 2.5zm15.89 11.27-3.8-1.67c-.13-.06-.28-.1-.44-.1H17V7.5C17 6.12 15.88 5 14.5 5S12 6.12 12 7.5v8.15l-1.87-.4c-.19-.03-1.02-.15-1.73.56L7 17.22l5.12 5.19c.37.38.88.59 1.41.59h6.55c.98 0 1.81-.7 1.97-1.67l.92-5.44c.15-.86-.29-1.72-1.08-2.12M20.08 21h-6.55l-3.7-3.78 4.17.89V7.5c0-.28.22-.5.5-.5s.5.22.5.5v6.18h1.76L21 15.56z\"\n}, \"1\")], 'PinchTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity"], "sources": ["G:/linkinblink/hotel-dashboard/node_modules/@mui/icons-material/esm/PinchTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"m21 15.56-4.24-1.89H15V7.5c0-.28-.22-.5-.5-.5s-.5.22-.5.5v10.61l-4.17-.89 3.7 3.78h6.55z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M6 2.5V1h5v5H9.5V3.56L3.56 9.5H6V11H1V6h1.5v2.44L8.44 2.5zm15.89 11.27-3.8-1.67c-.13-.06-.28-.1-.44-.1H17V7.5C17 6.12 15.88 5 14.5 5S12 6.12 12 7.5v8.15l-1.87-.4c-.19-.03-1.02-.15-1.73.56L7 17.22l5.12 5.19c.37.38.88.59 1.41.59h6.55c.98 0 1.81-.7 1.97-1.67l.92-5.44c.15-.86-.29-1.72-1.08-2.12M20.08 21h-6.55l-3.7-3.78 4.17.89V7.5c0-.28.22-.5.5-.5s.5.22.5.5v6.18h1.76L21 15.56z\"\n}, \"1\")], 'PinchTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE,0FAA0F;EAC7FC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}