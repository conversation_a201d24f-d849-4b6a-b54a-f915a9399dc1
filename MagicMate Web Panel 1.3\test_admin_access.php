<?php
session_start();
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>🔐 Admin Panel Access Test</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".success { color: green; }";
echo ".error { color: red; }";
echo ".warning { color: orange; }";
echo ".card { background: #f9f9f9; padding: 15px; margin: 10px 0; border-radius: 5px; }";
echo "form { background: #fff; padding: 20px; border-radius: 5px; border: 1px solid #ddd; max-width: 400px; }";
echo "input { width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; }";
echo "button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; }";
echo "button:hover { background: #005a87; }";
echo "</style>";
echo "</head><body>";

echo "<h1>🔐 Admin Panel Access Test</h1>";
echo "<hr>";

try {
    // Connect to database
    $evmulti = new mysqli("localhost", "u158044629_linkinblink", "K:l*&&yO7", "u158044629_linkinblink");
    $evmulti->set_charset("utf8mb4");
    
    if ($evmulti->connect_error) {
        throw new Exception("Database connection failed: " . $evmulti->connect_error);
    }
    
    echo "<div class='card success'>";
    echo "<h2>✅ Database Connection Successful</h2>";
    echo "</div>";
    
    // Check if admin table exists
    $admin_table_check = $evmulti->query("SHOW TABLES LIKE 'admin'");
    if ($admin_table_check->num_rows == 0) {
        echo "<div class='card error'>";
        echo "<h2>❌ Admin table not found</h2>";
        echo "<p>Available tables:</p>";
        $tables = $evmulti->query("SHOW TABLES");
        echo "<ul>";
        while ($table = $tables->fetch_array()) {
            echo "<li>" . $table[0] . "</li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div class='card success'>";
        echo "<h2>✅ Admin table exists</h2>";
        
        // Show admin users
        $admin_users = $evmulti->query("SELECT id, username FROM admin");
        echo "<p><strong>Admin users in database:</strong></p>";
        echo "<ul>";
        while ($admin = $admin_users->fetch_assoc()) {
            echo "<li>ID: " . $admin['id'] . " - Username: " . $admin['username'] . "</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // Handle login attempt
    if ($_POST['username'] && $_POST['password']) {
        $username = $evmulti->real_escape_string($_POST['username']);
        $password = $evmulti->real_escape_string($_POST['password']);
        
        echo "<div class='card'>";
        echo "<h3>🔍 Login Attempt</h3>";
        echo "<p><strong>Username:</strong> " . htmlspecialchars($username) . "</p>";
        echo "<p><strong>Password:</strong> " . str_repeat('*', strlen($password)) . "</p>";
        
        // Check credentials
        $login_query = "SELECT * FROM admin WHERE username = '$username' AND password = '$password'";
        echo "<p><strong>Query:</strong> " . $login_query . "</p>";
        
        $result = $evmulti->query($login_query);
        
        if ($result && $result->num_rows > 0) {
            $admin_data = $result->fetch_assoc();
            
            echo "<div class='success'>";
            echo "<h3>✅ Login Successful!</h3>";
            echo "<p><strong>Admin ID:</strong> " . $admin_data['id'] . "</p>";
            echo "<p><strong>Username:</strong> " . $admin_data['username'] . "</p>";
            echo "</div>";
            
            // Set session
            $_SESSION['evename'] = $admin_data['username'];
            $_SESSION['stype'] = 'mowner';
            
            echo "<div class='card'>";
            echo "<h3>🎯 Next Steps</h3>";
            echo "<p>✅ Session set successfully</p>";
            echo "<p><strong>Admin Panel Links:</strong></p>";
            echo "<ul>";
            echo "<li><a href='index.php' target='_blank'>Dashboard</a></li>";
            echo "<li><a href='list_user.php' target='_blank'>User List</a></li>";
            echo "<li><a href='setting.php' target='_blank'>Settings</a></li>";
            echo "</ul>";
            echo "</div>";
            
        } else {
            echo "<div class='error'>";
            echo "<h3>❌ Login Failed</h3>";
            echo "<p>Invalid username or password</p>";
            echo "</div>";
        }
        echo "</div>";
    }
    
    // Show login form
    echo "<div class='card'>";
    echo "<h3>🔐 Test Admin Login</h3>";
    echo "<form method='POST'>";
    echo "<label>Username:</label>";
    echo "<input type='text' name='username' value='admin' required>";
    echo "<label>Password:</label>";
    echo "<input type='password' name='password' value='admin@123' required>";
    echo "<button type='submit'>Test Login</button>";
    echo "</form>";
    echo "</div>";
    
    // Check current session
    echo "<div class='card'>";
    echo "<h3>📊 Current Session Status</h3>";
    if (isset($_SESSION['evename'])) {
        echo "<p class='success'>✅ Admin logged in: " . $_SESSION['evename'] . "</p>";
        echo "<p><strong>Session Type:</strong> " . $_SESSION['stype'] . "</p>";
    } else {
        echo "<p class='warning'>⚠️ No admin session active</p>";
    }
    echo "</div>";
    
    // Test user data access
    echo "<div class='card'>";
    echo "<h3>👥 User Data Access Test</h3>";
    
    $user_count = $evmulti->query("SELECT COUNT(*) as count FROM tbl_user")->fetch_assoc()['count'];
    echo "<p><strong>Total users in database:</strong> " . $user_count . "</p>";
    
    if ($user_count > 0) {
        echo "<p class='success'>✅ User data is accessible</p>";
        echo "<p><strong>Quick Links:</strong></p>";
        echo "<ul>";
        echo "<li><a href='view_user_data.php' target='_blank'>View Detailed User Data</a></li>";
        echo "<li><a href='list_user.php' target='_blank'>Admin User List Page</a></li>";
        echo "</ul>";
    } else {
        echo "<p class='warning'>⚠️ No users found in database</p>";
    }
    echo "</div>";
    
    $evmulti->close();
    
} catch (Exception $e) {
    echo "<div class='card error'>";
    echo "<h2>❌ Error</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</body></html>";
?>
