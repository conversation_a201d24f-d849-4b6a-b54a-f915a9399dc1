# 🔥 Firebase Admin Panel Integration Guide

## 🎯 **WHAT NEEDS TO BE DONE IN FIREBASE**

### **1. 🏗️ CREATE FIREBASE PROJECT STRUCTURE**

#### **Go to Firebase Console: https://console.firebase.google.com/project/linkinblink-f544a**

### **2. 📊 SET UP FIRESTORE DATABASE**

#### **Step 1: Enable Firestore**
```
Firebase Console → Firestore Database → Create database
- Choose "Start in production mode"
- Select location: asia-south1 (Mumbai)
```

#### **Step 2: Create Required Collections**
**Create these collections with sample documents:**

**Collection: MagicUser**
```json
Document ID: "1"
{
  "uid": "1",
  "name": "Test User",
  "email": "<EMAIL>",
  "mobile": "+************",
  "profilePic": "",
  "fcmToken": "sample_token",
  "isOnline": true,
  "lastActive": "2025-01-02T10:00:00Z",
  "createdAt": "2025-01-02T10:00:00Z",
  "appType": "event_user"
}
```

**Collection: MagicOrganizer**
```json
Document ID: "1"
{
  "uid": "1",
  "name": "Test Organizer",
  "email": "<EMAIL>",
  "mobile": "+************",
  "profilePic": "",
  "fcmToken": "sample_token",
  "isOnline": false,
  "lastActive": "2025-01-02T10:00:00Z",
  "createdAt": "2025-01-02T10:00:00Z",
  "appType": "event_organizer"
}
```

**Collection: Magic_Organization_rooms**
```json
Document ID: "U1_O1"
{
  "uid": "U1_O1",
  "name": "Test User",
  "email": "<EMAIL>",
  "image": "",
  "isOnline": true,
  "lastActive": "2025-01-02T10:00:00Z",
  "participants": ["1", "1"],
  "lastMessage": "Hello!",
  "lastMessageTime": "2025-01-02T10:00:00Z"
}
```

**Subcollection: Magic_Organization_rooms/U1_O1/messages**
```json
Document ID: auto-generated
{
  "content": "Hello, I need help with event booking",
  "senderId": "1",
  "receiverId": "1",
  "sentTime": "2025-01-02T10:00:00Z",
  "messageType": "text",
  "userType": "US"
}
```

**Collection: events**
```json
Document ID: "1"
{
  "eventId": "1",
  "title": "Sample Event",
  "description": "This is a sample event",
  "organizerId": "1",
  "startDate": "2025-01-15T18:00:00Z",
  "endDate": "2025-01-15T22:00:00Z",
  "location": "Mumbai, India",
  "price": 100,
  "availableTickets": 50,
  "totalTickets": 100,
  "status": "active",
  "createdAt": "2025-01-02T10:00:00Z",
  "updatedAt": "2025-01-02T10:00:00Z"
}
```

**Collection: bookings**
```json
Document ID: "1"
{
  "bookingId": "1",
  "userId": "1",
  "eventId": "1",
  "organizerId": "1",
  "ticketCount": 2,
  "totalAmount": 200,
  "bookingStatus": "confirmed",
  "paymentStatus": "paid",
  "bookingDate": "2025-01-02T10:00:00Z",
  "eventDate": "2025-01-15T18:00:00Z"
}
```

**Collection: hotels** (for merged app)
```json
Document ID: "1"
{
  "hotelId": "1",
  "name": "Sample Hotel",
  "description": "A beautiful hotel in Mumbai",
  "location": "Mumbai, India",
  "rating": 4.5,
  "amenities": ["wifi", "parking", "pool", "restaurant"],
  "images": ["https://example.com/hotel1.jpg"],
  "pricePerNight": 2000,
  "availableRooms": 10,
  "totalRooms": 20,
  "status": "active"
}
```

**Collection: hotel_bookings**
```json
Document ID: "1"
{
  "bookingId": "1",
  "userId": "1",
  "hotelId": "1",
  "checkInDate": "2025-01-15T14:00:00Z",
  "checkOutDate": "2025-01-17T11:00:00Z",
  "roomCount": 1,
  "guestCount": 2,
  "totalAmount": 4000,
  "bookingStatus": "confirmed",
  "paymentStatus": "paid"
}
```

**Collection: notifications**
```json
Document ID: auto-generated
{
  "userId": "1",
  "title": "Booking Confirmed",
  "body": "Your event booking has been confirmed",
  "type": "event_booking",
  "data": {
    "eventId": "1",
    "bookingId": "1"
  },
  "sentAt": "2025-01-02T10:00:00Z",
  "readAt": null,
  "status": "sent"
}
```

### **3. 🔐 SET UP SECURITY RULES**

#### **Firestore Rules:**
```
Firebase Console → Firestore → Rules → Copy this:
```

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // MagicUser collection
    match /MagicUser/{userId} {
      allow read, write: if request.auth != null;
    }
    
    // MagicOrganizer collection
    match /MagicOrganizer/{organizerId} {
      allow read, write: if request.auth != null;
    }
    
    // Magic_Organization_rooms collection
    match /Magic_Organization_rooms/{roomId} {
      allow read, write: if request.auth != null;
      
      match /messages/{messageId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // Events collection
    match /events/{eventId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // Bookings collection
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null;
    }
    
    // Hotels collection
    match /hotels/{hotelId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // Hotel bookings collection
    match /hotel_bookings/{bookingId} {
      allow read, write: if request.auth != null;
    }
    
    // Notifications collection
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### **4. 📱 ENABLE CLOUD MESSAGING**

#### **Step 1: Enable FCM**
```
Firebase Console → Project Settings → Cloud Messaging
- Click "Enable Cloud Messaging API"
```

#### **Step 2: Get Server Key**
```
Firebase Console → Project Settings → Cloud Messaging
- Copy "Server key" 
- Add to your backend configuration
```

#### **Step 3: Configure Topics**
```
Create these topics for notifications:
- all_users (for general announcements)
- event_users (for event-related notifications)
- hotel_users (for hotel-related notifications)
- organizers (for organizer notifications)
```

### **5. 🗄️ ENABLE FIREBASE STORAGE**

#### **Step 1: Enable Storage**
```
Firebase Console → Storage → Get started
- Choose "Start in production mode"
- Select location: asia-south1
```

#### **Step 2: Set Storage Rules**
```
Firebase Console → Storage → Rules → Copy this:
```

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

#### **Step 3: Create Folder Structure**
```
Create these folders in Storage:
/users/{user_id}/profile_pictures/
/users/{user_id}/documents/
/events/{event_id}/images/
/events/{event_id}/documents/
/hotels/{hotel_id}/images/
/hotels/{hotel_id}/documents/
/chat/{conversation_id}/images/
/chat/{conversation_id}/files/
```

### **6. 🔑 ENABLE AUTHENTICATION**

#### **Step 1: Enable Auth Methods**
```
Firebase Console → Authentication → Sign-in method
- Enable "Email/Password"
- Enable "Phone" (for OTP)
- Enable "Anonymous" (optional)
```

#### **Step 2: Configure Authorized Domains**
```
Firebase Console → Authentication → Settings → Authorized domains
- Add your domain: linkinblink.wipstertechnologies.com
- Add localhost for testing
```

### **7. 📊 ENABLE ANALYTICS (Optional)**

#### **Step 1: Enable Analytics**
```
Firebase Console → Analytics → Enable Google Analytics
- Create new Google Analytics account or link existing
- Choose location: India
```

#### **Step 2: Configure Events**
```
Track these custom events:
- user_registration
- event_booking
- hotel_booking
- payment_completed
- chat_message_sent
- app_opened
```

### **8. 🌐 ENABLE HOSTING (Optional)**

#### **Step 1: Enable Hosting**
```
Firebase Console → Hosting → Get started
- Can host admin panel documentation
- Can host API documentation
```

### **9. ⚙️ PROJECT SETTINGS CONFIGURATION**

#### **Step 1: General Settings**
```
Firebase Console → Project Settings → General
- Project name: LinkinBlink MagicMate
- Project ID: linkinblink-f544a
- Default GCP resource location: asia-south1
```

#### **Step 2: Your Apps**
```
Verify these apps are configured:
✅ Web App: 1:************:web:a9e5c8f4c2b8e9a9e5c8f4
✅ Android App: 1:************:android:a9e5c8f4c2b8e9a9e5c8f4
✅ iOS App: 1:************:ios:a9e5c8f4c2b8e9a9e5c8f4
```

### **10. 🔧 BACKEND INTEGRATION**

#### **Step 1: Add FCM Server Key to Admin Panel**
```
1. Login to admin panel
2. Go to Settings
3. Add new field: "FCM Server Key"
4. Save the server key from Firebase Console
```

#### **Step 2: Test Notification Service**
```
1. Upload firebase/notification_service.php to your server
2. Test with: https://your-domain.com/firebase/test_notifications.php
3. Verify notifications are sent to mobile apps
```

## ✅ **FIREBASE SETUP CHECKLIST**

### **Firebase Console Tasks:**
- [ ] Firestore Database enabled
- [ ] All 9 collections created with sample data
- [ ] Security rules configured
- [ ] Cloud Messaging enabled
- [ ] FCM Server Key obtained
- [ ] Storage enabled with rules
- [ ] Authentication methods enabled
- [ ] Analytics enabled (optional)
- [ ] Hosting enabled (optional)

### **Mobile App Integration:**
- [ ] firebase_options.dart configured
- [ ] FCM tokens being generated
- [ ] User profiles syncing to Firestore
- [ ] Chat functionality working
- [ ] Push notifications working
- [ ] Hotel booking integration working

### **Backend Integration:**
- [ ] FCM Server Key added to admin panel
- [ ] Notification service implemented
- [ ] Data sync between MySQL and Firestore
- [ ] Real-time updates working

## 🎯 **EXPECTED FUNCTIONALITY AFTER SETUP**

### **User Mobile App:**
- ✅ User registration syncs to Firestore
- ✅ Chat with organizers works
- ✅ Push notifications for bookings
- ✅ Real-time event updates
- ✅ Hotel booking integration

### **Organizer App:**
- ✅ Organizer registration syncs to Firestore
- ✅ Chat with users works
- ✅ Push notifications for new bookings
- ✅ Real-time booking updates

### **Admin Panel:**
- ✅ Send notifications to users
- ✅ View real-time chat messages
- ✅ Monitor app usage analytics
- ✅ Manage user data

**Complete this Firebase setup and all real-time features will work perfectly!** 🔥📱✅
