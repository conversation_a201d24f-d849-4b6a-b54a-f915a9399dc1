<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// BulkSMSInd API Configuration
define('BULKSMSIND_BASE_URL', 'http://sms.bulksmsind.in443');
define('BULKSMSIND_USERNAME', 'hmescan');
define('BULKSMSIND_API_KEY', 'e0117592-f761-4393-9772-31d4c0eb41cf');
define('BULKSMSIND_SENDER_NAME', 'INFORM');
define('BULKSMSIND_SMS_TYPE', 'TRANS');
define('BULKSMSIND_PE_ID', '1701159876885885613');
define('BULKSMSIND_TEMPLATE_ID', '1707172090686482394');

/**
 * Generate 6-digit OTP
 */
function generateOTP() {
    return sprintf("%06d", mt_rand(1, 999999));
}

/**
 * Send OTP via BulkSMSInd API
 */
function sendBulkSMSIndOTP($mobile) {
    try {
        // Generate OTP
        $otp = generateOTP();
        
        // Create OTP message using DLT approved template
        $message = "Your OTP for MagicMate verification is $otp. Valid for 10 minutes. Do not share with anyone.";
        
        // Prepare API URL
        $apiUrl = BULKSMSIND_BASE_URL . "/v2/sendSMS" .
                  "?username=" . BULKSMSIND_USERNAME .
                  "&message=" . urlencode($message) .
                  "&sendername=" . BULKSMSIND_SENDER_NAME .
                  "&smstype=" . BULKSMSIND_SMS_TYPE .
                  "&numbers=" . $mobile .
                  "&apikey=" . BULKSMSIND_API_KEY .
                  "&peid=" . BULKSMSIND_PE_ID .
                  "&templateid=" . BULKSMSIND_TEMPLATE_ID;
        
        // Log the API call
        error_log("BulkSMSInd API URL: " . $apiUrl);
        error_log("Sending OTP: $otp to mobile: $mobile");
        
        // Make API call
        $response = file_get_contents($apiUrl);
        
        if ($response === FALSE) {
            throw new Exception("Failed to make API call");
        }
        
        error_log("BulkSMSInd Response: " . $response);
        
        // Parse response
        $result = parseBulkSMSIndResponse($response, $otp, $mobile);
        
        return $result;
        
    } catch (Exception $e) {
        error_log("BulkSMSInd Exception: " . $e->getMessage());
        return [
            "Result" => "false",
            "ResponseCode" => "500",
            "ResponseMsg" => "Failed to send OTP: " . $e->getMessage(),
            "otp" => "",
            "msgId" => "",
            "status" => "failed"
        ];
    }
}

/**
 * Parse BulkSMSInd response
 */
function parseBulkSMSIndResponse($response, $otp, $mobile) {
    try {
        // BulkSMSInd typically returns responses like:
        // "Success|MessageID|Status" or "Error|ErrorMessage"
        
        $parts = explode('|', $response);
        
        if (!empty($parts) && stripos($parts[0], 'success') !== false) {
            return [
                "Result" => "true",
                "ResponseCode" => "200",
                "ResponseMsg" => "OTP sent successfully",
                "otp" => $otp,
                "msgId" => isset($parts[1]) ? $parts[1] : "",
                "status" => "sent"
            ];
        } else {
            return [
                "Result" => "false",
                "ResponseCode" => "400",
                "ResponseMsg" => isset($parts[1]) ? $parts[1] : "Failed to send SMS",
                "otp" => "",
                "msgId" => "",
                "status" => "failed"
            ];
        }
    } catch (Exception $e) {
        // If parsing fails, check if response contains success indicators
        if (stripos($response, 'success') !== false || 
            stripos($response, 'sent') !== false ||
            strpos($response, '200') !== false) {
            return [
                "Result" => "true",
                "ResponseCode" => "200",
                "ResponseMsg" => "OTP sent successfully",
                "otp" => $otp,
                "msgId" => "",
                "status" => "sent"
            ];
        } else {
            return [
                "Result" => "false",
                "ResponseCode" => "400",
                "ResponseMsg" => "Failed to send SMS",
                "otp" => "",
                "msgId" => "",
                "status" => "failed"
            ];
        }
    }
}

/**
 * Check SMS Balance
 */
function checkBulkSMSIndBalance() {
    try {
        $apiUrl = BULKSMSIND_BASE_URL . "/getSMSCredit" .
                  "?username=" . BULKSMSIND_USERNAME .
                  "&apikey=" . BULKSMSIND_API_KEY;
        
        $response = file_get_contents($apiUrl);
        
        if ($response === FALSE) {
            throw new Exception("Failed to check balance");
        }
        
        error_log("Balance Response: " . $response);
        
        return [
            "Result" => "true",
            "ResponseCode" => "200",
            "ResponseMsg" => "Balance retrieved successfully",
            "balance" => $response,
            "credits" => $response
        ];
        
    } catch (Exception $e) {
        error_log("Balance Check Exception: " . $e->getMessage());
        return [
            "Result" => "false",
            "ResponseCode" => "500",
            "ResponseMsg" => "Failed to check balance: " . $e->getMessage(),
            "balance" => "0",
            "credits" => "0"
        ];
    }
}

/**
 * Get Delivery Report
 */
function getBulkSMSIndDLR($msgId) {
    try {
        $apiUrl = BULKSMSIND_BASE_URL . "/getDLR" .
                  "?username=" . BULKSMSIND_USERNAME .
                  "&msgid=" . $msgId .
                  "&apikey=" . BULKSMSIND_API_KEY;
        
        $response = file_get_contents($apiUrl);
        
        if ($response === FALSE) {
            throw new Exception("Failed to get delivery report");
        }
        
        error_log("DLR Response: " . $response);
        
        return [
            "Result" => "true",
            "ResponseCode" => "200",
            "ResponseMsg" => "DLR retrieved successfully",
            "msgId" => $msgId,
            "status" => $response,
            "deliveryTime" => date('Y-m-d H:i:s'),
            "mobileNumber" => ""
        ];
        
    } catch (Exception $e) {
        error_log("DLR Exception: " . $e->getMessage());
        return [
            "Result" => "false",
            "ResponseCode" => "500",
            "ResponseMsg" => "Failed to get delivery report: " . $e->getMessage(),
            "msgId" => $msgId,
            "status" => "unknown",
            "deliveryTime" => "",
            "mobileNumber" => ""
        ];
    }
}

// Main execution
try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Get POST data
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception("Invalid JSON input");
        }
        
        // Validate required fields
        if (!isset($input['mobile']) || empty($input['mobile'])) {
            throw new Exception("Mobile number is required");
        }
        
        $mobile = $input['mobile'];
        
        // Validate mobile number format
        if (!preg_match('/^\+?[1-9]\d{1,14}$/', $mobile)) {
            throw new Exception("Invalid mobile number format");
        }
        
        // Check for action type
        $action = isset($input['action']) ? $input['action'] : 'send_otp';
        
        switch ($action) {
            case 'send_otp':
                $result = sendBulkSMSIndOTP($mobile);
                break;
                
            case 'check_balance':
                $result = checkBulkSMSIndBalance();
                break;
                
            case 'get_dlr':
                if (!isset($input['msgId'])) {
                    throw new Exception("Message ID is required for DLR");
                }
                $result = getBulkSMSIndDLR($input['msgId']);
                break;
                
            default:
                $result = sendBulkSMSIndOTP($mobile);
                break;
        }
        
        echo json_encode($result);
        
    } else {
        throw new Exception("Only POST method is allowed");
    }
    
} catch (Exception $e) {
    error_log("BulkSMSInd API Error: " . $e->getMessage());
    
    $errorResponse = [
        "Result" => "false",
        "ResponseCode" => "400",
        "ResponseMsg" => $e->getMessage(),
        "otp" => "",
        "msgId" => "",
        "status" => "error"
    ];
    
    echo json_encode($errorResponse);
}
?>
