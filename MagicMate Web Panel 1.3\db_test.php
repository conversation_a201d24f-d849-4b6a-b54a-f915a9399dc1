<?php
// Database connection test
header('Content-Type: application/json');

try {
    // Test database connection
    $evmulti = new mysqli("localhost", "username", "password", "database");
    
    if ($evmulti->connect_error) {
        throw new Exception("Connection failed: " . $evmulti->connect_error);
    }
    
    // Test admin table
    $admin_result = $evmulti->query("SELECT COUNT(*) as count FROM tbl_admin");
    $admin_count = $admin_result ? $admin_result->fetch_assoc()['count'] : 0;
    
    // Test settings table
    $settings_result = $evmulti->query("SELECT COUNT(*) as count FROM tbl_setting");
    $settings_count = $settings_result ? $settings_result->fetch_assoc()['count'] : 0;
    
    // Get first admin username
    $first_admin_result = $evmulti->query("SELECT username FROM tbl_admin LIMIT 1");
    $first_admin = $first_admin_result ? $first_admin_result->fetch_assoc()['username'] : 'none';
    
    $response = [
        'status' => 'success',
        'message' => 'Database connection successful',
        'admin_count' => $admin_count,
        'settings_count' => $settings_count,
        'first_admin_username' => $first_admin,
        'server_info' => $evmulti->server_info,
        'php_version' => PHP_VERSION
    ];
    
    $evmulti->close();
    
} catch (Exception $e) {
    $response = [
        'status' => 'error',
        'message' => $e->getMessage(),
        'php_version' => PHP_VERSION
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>
