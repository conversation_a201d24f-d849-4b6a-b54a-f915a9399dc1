<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Debug User Data Storage Investigation
echo "<h1>🔍 USER DATA STORAGE INVESTIGATION</h1>";
echo "<hr>";

try {
    // Step 1: Test database connection
    echo "<h2>📊 Database Connection Test</h2>";
    
    // Include the config file
    require dirname(__FILE__) . '/filemanager/evconfing.php';

    // Also test direct connection
    $direct_connection = new mysqli("localhost", "u158044629_linkinblink", "K:l*&&yO7", "u158044629_linkinblink");
    
    if ($evmulti->connect_error) {
        echo "<p style='color: red;'>❌ Database connection failed: " . $evmulti->connect_error . "</p>";
        exit();
    } else {
        echo "<p style='color: green;'>✅ Database connected successfully</p>";
        echo "<p><strong>Server Info:</strong> " . $evmulti->server_info . "</p>";
    }
    
    // Step 2: Check if tbl_user table exists
    echo "<h2>🗄️ Table Structure Check</h2>";
    
    $tables_check = $evmulti->query("SHOW TABLES LIKE 'tbl_user'");
    if ($tables_check->num_rows > 0) {
        echo "<p style='color: green;'>✅ tbl_user table exists</p>";
        
        // Get table structure
        $structure = $evmulti->query("DESCRIBE tbl_user");
        echo "<h3>📋 tbl_user Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = $structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p style='color: red;'>❌ tbl_user table does not exist</p>";
    }
    
    // Step 3: Count users in database
    echo "<h2>👥 User Data Analysis</h2>";
    
    $user_count = $evmulti->query("SELECT COUNT(*) as count FROM tbl_user");
    if ($user_count) {
        $count_result = $user_count->fetch_assoc();
        echo "<p><strong>Total Users in Database:</strong> " . $count_result['count'] . "</p>";
        
        if ($count_result['count'] > 0) {
            // Show recent users
            echo "<h3>📱 Recent User Registrations (Last 10):</h3>";
            $recent_users = $evmulti->query("SELECT id, name, email, mobile, reg_date, status FROM tbl_user ORDER BY id DESC LIMIT 10");
            
            if ($recent_users->num_rows > 0) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Mobile</th><th>Registration Date</th><th>Status</th></tr>";
                while ($user = $recent_users->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . $user['id'] . "</td>";
                    echo "<td>" . $user['name'] . "</td>";
                    echo "<td>" . $user['email'] . "</td>";
                    echo "<td>" . $user['mobile'] . "</td>";
                    echo "<td>" . $user['reg_date'] . "</td>";
                    echo "<td>" . ($user['status'] == 1 ? '✅ Active' : '❌ Inactive') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
            // Check for duplicate emails/mobiles
            echo "<h3>🔍 Duplicate Check:</h3>";
            $duplicate_emails = $evmulti->query("SELECT email, COUNT(*) as count FROM tbl_user GROUP BY email HAVING count > 1");
            $duplicate_mobiles = $evmulti->query("SELECT mobile, COUNT(*) as count FROM tbl_user GROUP BY mobile HAVING count > 1");
            
            echo "<p><strong>Duplicate Emails:</strong> " . $duplicate_emails->num_rows . "</p>";
            echo "<p><strong>Duplicate Mobiles:</strong> " . $duplicate_mobiles->num_rows . "</p>";
            
            if ($duplicate_emails->num_rows > 0) {
                echo "<h4>📧 Duplicate Email Addresses:</h4>";
                while ($dup = $duplicate_emails->fetch_assoc()) {
                    echo "<p>Email: " . $dup['email'] . " (Count: " . $dup['count'] . ")</p>";
                }
            }
            
            if ($duplicate_mobiles->num_rows > 0) {
                echo "<h4>📱 Duplicate Mobile Numbers:</h4>";
                while ($dup = $duplicate_mobiles->fetch_assoc()) {
                    echo "<p>Mobile: " . $dup['mobile'] . " (Count: " . $dup['count'] . ")</p>";
                }
            }
        } else {
            echo "<p style='color: orange;'>⚠️ No users found in database</p>";
        }
    }
    
    // Step 4: Check admin panel access
    echo "<h2>🖥️ Admin Panel Access Check</h2>";
    
    // Check if admin table exists
    $admin_check = $evmulti->query("SHOW TABLES LIKE 'admin'");
    if ($admin_check->num_rows > 0) {
        echo "<p style='color: green;'>✅ admin table exists</p>";
        
        $admin_count = $evmulti->query("SELECT COUNT(*) as count FROM admin");
        $admin_result = $admin_count->fetch_assoc();
        echo "<p><strong>Admin Users:</strong> " . $admin_result['count'] . "</p>";
        
        if ($admin_result['count'] > 0) {
            $admin_users = $evmulti->query("SELECT username FROM admin");
            echo "<p><strong>Admin Usernames:</strong> ";
            while ($admin = $admin_users->fetch_assoc()) {
                echo $admin['username'] . " ";
            }
            echo "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ admin table does not exist</p>";
    }
    
    // Step 5: Test registration API endpoint
    echo "<h2>🔗 API Endpoint Test</h2>";
    
    $api_file = dirname(__FILE__) . '/user_api/u_reg_user.php';
    if (file_exists($api_file)) {
        echo "<p style='color: green;'>✅ Registration API file exists: user_api/u_reg_user.php</p>";
    } else {
        echo "<p style='color: red;'>❌ Registration API file missing</p>";
    }
    
    // Step 6: Check database configuration
    echo "<h2>⚙️ Database Configuration</h2>";
    
    $config_file = dirname(__FILE__) . '/filemanager/evconfing.php';
    if (file_exists($config_file)) {
        echo "<p style='color: green;'>✅ Database config file exists</p>";
        
        // Read config file content (safely)
        $config_content = file_get_contents($config_file);
        if (strpos($config_content, 'localhost') !== false) {
            echo "<p><strong>Database Host:</strong> localhost</p>";
        }
        if (strpos($config_content, 'username') !== false) {
            echo "<p><strong>Database User:</strong> username (placeholder)</p>";
        }
        if (strpos($config_content, 'database') !== false) {
            echo "<p><strong>Database Name:</strong> database (placeholder)</p>";
        }
        
        echo "<p style='color: orange;'>⚠️ Database credentials appear to be placeholders</p>";
        echo "<p><strong>Action Required:</strong> Update database credentials in filemanager/evconfing.php</p>";
    }
    
    // Step 7: Check list_user.php page
    echo "<h2>📋 Admin User List Page</h2>";
    
    $list_user_file = dirname(__FILE__) . '/list_user.php';
    if (file_exists($list_user_file)) {
        echo "<p style='color: green;'>✅ User list page exists: list_user.php</p>";
        echo "<p><strong>Access URL:</strong> <a href='list_user.php' target='_blank'>list_user.php</a></p>";
    } else {
        echo "<p style='color: red;'>❌ User list page missing</p>";
    }
    
    // Step 8: Summary and recommendations
    echo "<h2>📝 Summary & Recommendations</h2>";
    
    echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎯 Key Findings:</h3>";
    echo "<ul>";
    
    if ($count_result['count'] > 0) {
        echo "<li style='color: green;'>✅ Users ARE being stored in database (" . $count_result['count'] . " users found)</li>";
        echo "<li style='color: green;'>✅ Registration API is working</li>";
        echo "<li style='color: orange;'>⚠️ Admin panel may have display issues</li>";
    } else {
        echo "<li style='color: red;'>❌ No users found in database</li>";
        echo "<li style='color: orange;'>⚠️ Registration may not be working</li>";
    }
    
    echo "</ul>";
    
    echo "<h3>🔧 Action Items:</h3>";
    echo "<ol>";
    echo "<li><strong>Update Database Credentials:</strong> Edit filemanager/evconfing.php with real database details</li>";
    echo "<li><strong>Test Admin Login:</strong> Try logging into admin panel</li>";
    echo "<li><strong>Check User List:</strong> Access list_user.php after login</li>";
    echo "<li><strong>Test Registration:</strong> Try registering a new user from mobile app</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><strong>Debug completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
