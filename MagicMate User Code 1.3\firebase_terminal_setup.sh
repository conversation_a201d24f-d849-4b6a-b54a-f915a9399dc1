#!/bin/bash

# Firebase Terminal Setup Script for MagicMate
# This script will set up Firebase project completely through terminal

echo "🔥 Firebase Terminal Setup for MagicMate"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project configuration
PROJECT_ID="linkinblink-f544a"
REGION="asia-south1"

echo -e "${BLUE}Project ID: $PROJECT_ID${NC}"
echo -e "${BLUE}Region: $REGION${NC}"
echo ""

# Step 1: Install Firebase CLI
echo -e "${YELLOW}Step 1: Installing Firebase CLI...${NC}"
if ! command -v firebase &> /dev/null; then
    echo "Installing Firebase CLI..."
    npm install -g firebase-tools
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Firebase CLI installed successfully${NC}"
    else
        echo -e "${RED}❌ Failed to install Firebase CLI${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ Firebase CLI already installed${NC}"
fi

# Step 2: Login to Firebase
echo -e "${YELLOW}Step 2: Logging into Firebase...${NC}"
echo "This will open a browser window for authentication"
firebase login --interactive

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Successfully logged into Firebase${NC}"
else
    echo -e "${RED}❌ Failed to login to Firebase${NC}"
    exit 1
fi

# Step 3: Set Firebase project
echo -e "${YELLOW}Step 3: Setting Firebase project...${NC}"
firebase use $PROJECT_ID

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Successfully set project to $PROJECT_ID${NC}"
else
    echo -e "${RED}❌ Failed to set project. Creating project...${NC}"
    firebase projects:create $PROJECT_ID --display-name "LinkinBlink MagicMate"
    firebase use $PROJECT_ID
fi

# Step 4: Initialize Firebase features
echo -e "${YELLOW}Step 4: Initializing Firebase features...${NC}"

# Create firebase.json configuration
cat > firebase.json << EOF
{
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "storage": {
    "rules": "storage.rules"
  },
  "functions": {
    "source": "functions",
    "runtime": "nodejs18"
  },
  "hosting": {
    "public": "public",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ]
  }
}
EOF

echo -e "${GREEN}✅ Created firebase.json configuration${NC}"

# Step 5: Create Firestore security rules
echo -e "${YELLOW}Step 5: Creating Firestore security rules...${NC}"

cat > firestore.rules << 'EOF'
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // MagicUser collection - users can read/write their own data
    match /MagicUser/{userId} {
      allow read, write: if request.auth != null;
    }
    
    // MagicOrganizer collection - organizers can read/write their own data
    match /MagicOrganizer/{organizerId} {
      allow read, write: if request.auth != null;
    }
    
    // Magic_Organization_rooms - chat rooms
    match /Magic_Organization_rooms/{roomId} {
      allow read, write: if request.auth != null;
      
      // Messages subcollection
      match /messages/{messageId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // Events collection - read for all, write for organizers
    match /events/{eventId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // Bookings collection - users can read/write their own bookings
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null;
    }
    
    // Hotels collection - read for all, write for admins
    match /hotels/{hotelId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // Hotel bookings collection
    match /hotel_bookings/{bookingId} {
      allow read, write: if request.auth != null;
    }
    
    // Notifications collection
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null;
    }
  }
}
EOF

echo -e "${GREEN}✅ Created Firestore security rules${NC}"

# Step 6: Create Storage security rules
echo -e "${YELLOW}Step 6: Creating Storage security rules...${NC}"

cat > storage.rules << 'EOF'
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
EOF

echo -e "${GREEN}✅ Created Storage security rules${NC}"

# Step 7: Create Firestore indexes
echo -e "${YELLOW}Step 7: Creating Firestore indexes...${NC}"

cat > firestore.indexes.json << 'EOF'
{
  "indexes": [
    {
      "collectionGroup": "messages",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "sentTime",
          "order": "ASCENDING"
        }
      ]
    },
    {
      "collectionGroup": "Magic_Organization_rooms",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "lastActive",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "events",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "startDate",
          "order": "ASCENDING"
        }
      ]
    },
    {
      "collectionGroup": "bookings",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "bookingDate",
          "order": "DESCENDING"
        }
      ]
    }
  ],
  "fieldOverrides": []
}
EOF

echo -e "${GREEN}✅ Created Firestore indexes${NC}"

# Step 8: Deploy Firestore rules and indexes
echo -e "${YELLOW}Step 8: Deploying Firestore rules and indexes...${NC}"
firebase deploy --only firestore

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Successfully deployed Firestore rules and indexes${NC}"
else
    echo -e "${RED}❌ Failed to deploy Firestore rules${NC}"
fi

# Step 9: Deploy Storage rules
echo -e "${YELLOW}Step 9: Deploying Storage rules...${NC}"
firebase deploy --only storage

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Successfully deployed Storage rules${NC}"
else
    echo -e "${RED}❌ Failed to deploy Storage rules${NC}"
fi

# Step 10: Enable required APIs
echo -e "${YELLOW}Step 10: Enabling required Firebase APIs...${NC}"

# Enable Firestore API
gcloud services enable firestore.googleapis.com --project=$PROJECT_ID
echo -e "${GREEN}✅ Enabled Firestore API${NC}"

# Enable Cloud Messaging API
gcloud services enable fcm.googleapis.com --project=$PROJECT_ID
echo -e "${GREEN}✅ Enabled Cloud Messaging API${NC}"

# Enable Storage API
gcloud services enable storage.googleapis.com --project=$PROJECT_ID
echo -e "${GREEN}✅ Enabled Storage API${NC}"

# Enable Authentication API
gcloud services enable identitytoolkit.googleapis.com --project=$PROJECT_ID
echo -e "${GREEN}✅ Enabled Authentication API${NC}"

echo ""
echo -e "${GREEN}🎉 Firebase Terminal Setup Complete!${NC}"
echo ""
echo -e "${BLUE}Next Steps:${NC}"
echo "1. Run: ./create_firestore_collections.sh"
echo "2. Run: ./setup_firebase_auth.sh"
echo "3. Run: ./configure_cloud_messaging.sh"
echo ""
echo -e "${YELLOW}Project URL: https://console.firebase.google.com/project/$PROJECT_ID${NC}"
