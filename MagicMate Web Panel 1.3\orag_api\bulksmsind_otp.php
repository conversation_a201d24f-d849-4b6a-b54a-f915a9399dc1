<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require dirname(dirname(__FILE__)) . '/filemanager/evconfing.php';

/**
 * Generate 6-digit OTP
 */
function generateOTP() {
    return sprintf("%06d", mt_rand(1, 999999));
}

/**
 * Send OTP via BulkSMSInd API
 */
function sendBulkSMSIndOTP($mobile, $set) {
    try {
        // Generate OTP
        $otp = generateOTP();
        
        // Clean mobile number
        $cleanMobile = preg_replace('/[^0-9]/', '', $mobile);
        
        // Ensure mobile number format (add 91 if needed)
        if (strlen($cleanMobile) == 10) {
            $cleanMobile = '91' . $cleanMobile;
        }
        
        // Create OTP message using DLT approved template
        $message = "Your OTP for MagicMate verification is $otp. Valid for 10 minutes. Do not share with anyone.";
        
        // Get BulkSMSInd configuration from settings
        $username = isset($set['bulksmsind_username']) ? $set['bulksmsind_username'] : 'hmescan';
        $apiKey = isset($set['bulksmsind_api_key']) ? $set['bulksmsind_api_key'] : 'e0117592-f761-4393-9772-31d4c0eb41cf';
        $senderName = isset($set['bulksmsind_sender']) ? $set['bulksmsind_sender'] : 'INFORM';
        $peId = isset($set['bulksmsind_pe_id']) ? $set['bulksmsind_pe_id'] : '1701159876885885613';
        $templateId = isset($set['bulksmsind_template_id']) ? $set['bulksmsind_template_id'] : '1707172090686482394';
        
        // Prepare API URL
        $apiUrl = "http://sms.bulksmsind.in/v2/sendSMS" .
                  "?username=" . urlencode($username) .
                  "&message=" . urlencode($message) .
                  "&sendername=" . urlencode($senderName) .
                  "&smstype=TRANS" .
                  "&numbers=" . $cleanMobile .
                  "&apikey=" . urlencode($apiKey) .
                  "&peid=" . urlencode($peId) .
                  "&templateid=" . urlencode($templateId);
        
        // Log the API call
        error_log("BulkSMSInd API URL: " . $apiUrl);
        error_log("Sending OTP: $otp to mobile: $cleanMobile");
        
        // Make API call
        $response = file_get_contents($apiUrl);
        
        if ($response === FALSE) {
            throw new Exception("Failed to make API call");
        }
        
        error_log("BulkSMSInd Response: " . $response);
        
        // Parse response
        $result = parseBulkSMSIndResponse($response, $otp, $cleanMobile);
        
        return $result;
        
    } catch (Exception $e) {
        error_log("BulkSMSInd Exception: " . $e->getMessage());
        return [
            "Result" => "false",
            "ResponseCode" => "500",
            "ResponseMsg" => "Failed to send OTP: " . $e->getMessage(),
            "otp" => "",
            "msgId" => "",
            "status" => "failed"
        ];
    }
}

/**
 * Parse BulkSMSInd response
 */
function parseBulkSMSIndResponse($response, $otp, $mobile) {
    try {
        // Clean the response
        $cleanResponse = trim($response);
        
        error_log("Parsing BulkSMSInd response: '$cleanResponse'");
        
        // Check if it's JSON
        if (substr($cleanResponse, 0, 1) === '{' && substr($cleanResponse, -1) === '}') {
            $jsonResponse = json_decode($cleanResponse, true);
            if ($jsonResponse && (isset($jsonResponse['status']) || isset($jsonResponse['success']))) {
                if ($jsonResponse['status'] === 'success' || $jsonResponse['success'] === true) {
                    return [
                        "Result" => "true",
                        "ResponseCode" => "200",
                        "ResponseMsg" => "OTP sent successfully",
                        "otp" => $otp,
                        "msgId" => isset($jsonResponse['msgId']) ? $jsonResponse['msgId'] : "",
                        "status" => "sent"
                    ];
                }
            }
        }
        
        // Check for pipe-separated format
        $parts = explode('|', $cleanResponse);
        
        if (count($parts) >= 2) {
            $firstPart = strtolower(trim($parts[0]));
            
            if (strpos($firstPart, 'success') !== false || $firstPart === '200') {
                return [
                    "Result" => "true",
                    "ResponseCode" => "200",
                    "ResponseMsg" => "OTP sent successfully",
                    "otp" => $otp,
                    "msgId" => isset($parts[1]) ? trim($parts[1]) : "",
                    "status" => "sent"
                ];
            }
        }
        
        // Check for common success indicators
        $lowerResponse = strtolower($cleanResponse);
        if (strpos($lowerResponse, 'success') !== false ||
            strpos($lowerResponse, 'sent') !== false ||
            strpos($lowerResponse, 'delivered') !== false ||
            strpos($lowerResponse, 'accepted') !== false ||
            strpos($cleanResponse, '200') !== false ||
            preg_match('/^[1-9]\d*$/', $cleanResponse)) { // Numeric message ID
            
            return [
                "Result" => "true",
                "ResponseCode" => "200",
                "ResponseMsg" => "OTP sent successfully",
                "otp" => $otp,
                "msgId" => $cleanResponse,
                "status" => "sent"
            ];
        }
        
        // If we reach here, consider it a failure
        return [
            "Result" => "false",
            "ResponseCode" => "400",
            "ResponseMsg" => "SMS sending failed: $cleanResponse",
            "otp" => "",
            "msgId" => "",
            "status" => "failed"
        ];
        
    } catch (Exception $e) {
        error_log("Parse Exception: " . $e->getMessage());
        return [
            "Result" => "false",
            "ResponseCode" => "500",
            "ResponseMsg" => "Failed to parse response: " . $e->getMessage(),
            "otp" => "",
            "msgId" => "",
            "status" => "error"
        ];
    }
}

// Main execution
try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Get POST data
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            // Try to get from form data
            $mobile = isset($_POST['mobile']) ? $_POST['mobile'] : '';
        } else {
            $mobile = isset($input['mobile']) ? $input['mobile'] : '';
        }
        
        if (empty($mobile)) {
            throw new Exception("Mobile number is required");
        }
        
        // Validate mobile number format
        if (!preg_match('/^\+?[1-9]\d{1,14}$/', $mobile)) {
            throw new Exception("Invalid mobile number format");
        }
        
        // Send OTP
        $result = sendBulkSMSIndOTP($mobile, $set);
        
        echo json_encode($result);
        
    } else if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Handle GET request for testing
        $mobile = isset($_GET['mobile']) ? $_GET['mobile'] : '';
        
        if (empty($mobile)) {
            throw new Exception("Mobile number is required");
        }
        
        // Send OTP
        $result = sendBulkSMSIndOTP($mobile, $set);
        
        echo json_encode($result);
        
    } else {
        throw new Exception("Only POST and GET methods are allowed");
    }
    
} catch (Exception $e) {
    error_log("BulkSMSInd API Error: " . $e->getMessage());
    
    $errorResponse = [
        "Result" => "false",
        "ResponseCode" => "400",
        "ResponseMsg" => $e->getMessage(),
        "otp" => "",
        "msgId" => "",
        "status" => "error"
    ];
    
    echo json_encode($errorResponse);
}
?>
