# 📱 BulkSMSInd SMS API - Manual Testing Guide

## 🔍 **URL FORMAT ANALYSIS**

### **Your Provided URL (with issues):**
```
http://sms.bulksmsind.in443/v2/sendSMS?username=hmescan&message=XXXXXXXXXX&sendername=XYZ&smstype=TRANS&numbers=+************apikey=e0117592-f761-4393-9772-31d4c0eb41cf&peid=1701163403411961935&templateid=1707174566472523986
```

### **Issues Found:**
1. ❌ Port format: `bulksmsind.in443` should be `bulksmsind.in:443`
2. ❌ Missing separator: `numbers=+************apikey=` should be `numbers=+************&apikey=`

### **✅ Corrected URL Format:**
```
http://sms.bulksmsind.in:443/v2/sendSMS?username=hmescan&message=XXXXXXXXXX&sendername=XYZ&smstype=TRANS&numbers=+************&apikey=e0117592-f761-4393-9772-31d4c0eb41cf&peid=1701163403411961935&templateid=1707174566472523986
```

## 🧪 **MANUAL TESTING STEPS**

### **Step 1: Test with Browser (Basic Test)**

#### **Create Test URL:**
```
http://sms.bulksmsind.in:443/v2/sendSMS?username=hmescan&message=Test%20message%20from%20MagicMate&sendername=INFORM&smstype=TRANS&numbers=+************&apikey=e0117592-f761-4393-9772-31d4c0eb41cf&peid=1701163403411961935&templateid=1707174566472523986
```

#### **Instructions:**
1. **Copy the URL above**
2. **Paste in browser address bar**
3. **Press Enter**
4. **Check response** (should show JSON response)
5. **Check your phone** (+************) for SMS

#### **Expected Response:**
```json
{
  "status": "success",
  "message": "SMS sent successfully",
  "messageId": "********"
}
```

### **Step 2: Test with cURL (Command Line)**

#### **Windows PowerShell:**
```powershell
curl "http://sms.bulksmsind.in:443/v2/sendSMS?username=hmescan&message=Test%20SMS%20from%20cURL&sendername=INFORM&smstype=TRANS&numbers=+************&apikey=e0117592-f761-4393-9772-31d4c0eb41cf&peid=1701163403411961935&templateid=1707174566472523986"
```

#### **Linux/Mac Terminal:**
```bash
curl "http://sms.bulksmsind.in:443/v2/sendSMS?username=hmescan&message=Test%20SMS%20from%20cURL&sendername=INFORM&smstype=TRANS&numbers=+************&apikey=e0117592-f761-4393-9772-31d4c0eb41cf&peid=1701163403411961935&templateid=1707174566472523986"
```

### **Step 3: Test with Postman**

#### **Setup:**
1. **Open Postman**
2. **Create new GET request**
3. **URL**: `http://sms.bulksmsind.in:443/v2/sendSMS`

#### **Parameters:**
```
username: hmescan
message: Test SMS from Postman
sendername: INFORM
smstype: TRANS
numbers: +************
apikey: e0117592-f761-4393-9772-31d4c0eb41cf
peid: 1701163403411961935
templateid: 1707174566472523986
```

#### **Send Request and Check:**
- **Response status**: Should be 200 OK
- **Response body**: JSON with success message
- **Phone**: Check for received SMS

### **Step 4: Test Different Message Types**

#### **Test 1: Simple Text Message**
```
message: Hello from MagicMate app
```

#### **Test 2: OTP Message**
```
message: Your OTP is 123456. Valid for 5 minutes.
```

#### **Test 3: Event Booking Confirmation**
```
message: Your event booking is confirmed. Booking ID: MB12345
```

## 🔍 **TROUBLESHOOTING GUIDE**

### **Common Issues & Solutions:**

#### **1. ❌ Connection Timeout**
**Problem**: Cannot connect to server
**Solutions**:
- Try without port: `http://sms.bulksmsind.in/v2/sendSMS`
- Try HTTPS: `https://sms.bulksmsind.in/v2/sendSMS`
- Check internet connection

#### **2. ❌ Authentication Failed**
**Problem**: Invalid username/API key
**Solutions**:
- Verify username: `hmescan`
- Verify API key: `e0117592-f761-4393-9772-31d4c0eb41cf`
- Check account status with BulkSMSInd

#### **3. ❌ Invalid Template ID**
**Problem**: Template not found
**Solutions**:
- Verify template ID: `1707174566472523986`
- Check if template is approved
- Contact BulkSMSInd support

#### **4. ❌ Invalid PE ID**
**Problem**: PE ID not recognized
**Solutions**:
- Verify PE ID: `1701163403411961935`
- Check DLT registration
- Ensure PE ID is active

### **Response Codes:**

#### **✅ Success Responses:**
```json
{
  "status": "success",
  "message": "SMS sent successfully",
  "messageId": "********"
}
```

#### **❌ Error Responses:**
```json
{
  "status": "error",
  "message": "Invalid API key",
  "code": "AUTH_FAILED"
}
```

```json
{
  "status": "error", 
  "message": "Insufficient balance",
  "code": "LOW_BALANCE"
}
```

## 🧪 **STEP-BY-STEP TESTING CHECKLIST**

### **Pre-Testing:**
- [ ] Verify your phone number: +************
- [ ] Ensure phone has network coverage
- [ ] Check BulkSMSInd account balance
- [ ] Confirm template is approved

### **Test 1: Browser Test**
- [ ] Copy corrected URL
- [ ] Paste in browser
- [ ] Check response in browser
- [ ] Check phone for SMS
- [ ] Note response time

### **Test 2: cURL Test**
- [ ] Open command prompt/terminal
- [ ] Run cURL command
- [ ] Check response in terminal
- [ ] Check phone for SMS
- [ ] Compare with browser test

### **Test 3: Postman Test**
- [ ] Setup Postman request
- [ ] Add all parameters
- [ ] Send request
- [ ] Check response
- [ ] Check phone for SMS

### **Test 4: Different Numbers**
- [ ] Test with different mobile number
- [ ] Test with multiple numbers (comma-separated)
- [ ] Test with international format
- [ ] Test with Indian format (without +91)

## 📱 **QUICK TEST URLs**

### **Test URL 1 (Basic):**
```
http://sms.bulksmsind.in/v2/sendSMS?username=hmescan&message=Test%20SMS&sendername=INFORM&smstype=TRANS&numbers=+************&apikey=e0117592-f761-4393-9772-31d4c0eb41cf&peid=1701163403411961935&templateid=1707174566472523986
```

### **Test URL 2 (Without Port):**
```
http://sms.bulksmsind.in/v2/sendSMS?username=hmescan&message=Test%20SMS%20No%20Port&sendername=INFORM&smstype=TRANS&numbers=+************&apikey=e0117592-f761-4393-9772-31d4c0eb41cf&peid=1701163403411961935&templateid=1707174566472523986
```

### **Test URL 3 (HTTPS):**
```
https://sms.bulksmsind.in/v2/sendSMS?username=hmescan&message=Test%20HTTPS&sendername=INFORM&smstype=TRANS&numbers=+************&apikey=e0117592-f761-4393-9772-31d4c0eb41cf&peid=1701163403411961935&templateid=1707174566472523986
```

## 📞 **EXPECTED RESULTS**

### **✅ If Working Correctly:**
1. **Browser**: Shows JSON success response
2. **Phone**: Receives SMS within 1-2 minutes
3. **Response Time**: Under 5 seconds
4. **Message Format**: Clean, readable text

### **❌ If Not Working:**
1. **Check account balance** with BulkSMSInd
2. **Verify template approval** status
3. **Contact BulkSMSInd support** with error details
4. **Test with different phone number**

## 🎯 **FINAL VERIFICATION**

After successful manual testing:
- [ ] SMS received on phone
- [ ] Response shows success
- [ ] Message content is correct
- [ ] Sender name shows as "INFORM"
- [ ] No error messages

**If all tests pass, the API is working correctly and ready for integration!** ✅📱
