<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<title>MagicMate - DOCUMENTATIONS</title>
<link rel="stylesheet" href="css/prism.css" />
<link rel="stylesheet" href="css/style.css" />
<link rel="stylesheet" href="css/custom.css" />
<link rel="shortcut icon" href="img/logo.png" type="image/x-icon">
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500;600;700;800;900&amp;display=swap" rel="stylesheet">
<script src="js/jquery-1.11.0.min.js"></script>
<script src="js/jquery.easing.js"></script>
<script src="js/jquery.scrollTo.js"></script>
<script src="js/prism.js"></script>
<script src="js/clipboard.min.js"></script>
<script src="js/perfectscroll.min.js"></script>
<script src="js/script.js"></script>
<script src="js/mm.js"></script>
<style>
         h7 {
         background: none repeat scroll 0 0 #d8d8d8;
         border-radius: 8px;
         color: #019cf5;
         font-size: 10px;
         font-style: italic;
         font-weight: 700;
         margin: 18px 0;
         padding: 5px 15px;
         }
         a {
         color: #019cf5;
         }
      </style>
</head>
<body>
<div class="wrapper-left">
<div class="logo">
<img src="img/logo.png" alt="logo" class="img-fluid" width="100" />
</div>
<div class="nav-wrapper">
<nav>
<ul>
<li><a href="#app-intro">Introduction</a></li>
<li>
<a href="#intro">Prerequisite Tools & Setup</a>
<ul class="sub-menu">
<li><a href="#intro">Introduction to Flutter</a></li>
<li><a href="#tools">Tools & Setup</a></li>
<li><a href="#setup-window">Android Studio – Windows</a></li>
<li><a href="#setup-mac">Android Studio – macOS</a></li>
<li><a href="#setup-linux">Android Studio – Linux</a></li>
</ul>
</li>
<li><a href="#build-run">Getting Started (Build & Run) </a></li>
<li>
<a href="#config-android">App Configuration</a>
<ul class="sub-menu">
<li><a href="#config-android">Android Configuration</a></li>
<li><a href="#config-ios">iOS Configuration</a></li>
<li><a href="#config-flutter">Flutter Configuration </a></li>
<li><a href="#baseurl-flutter">Change Base URL </a></li>
</ul>
</li>



<li><a href="#mm-firebase">Firebase Configuration</a></li>
<li><a href="#mm-firebase-rules">Firebase Rules</a></li>
<li><a href="#mm-firebase-recaptcha">Mandatory Setup</a></li>
<li><a href="#mm-onesignal">Onesignal Setup</a></li>

<li>
<a href="#">Admin Panel Setup</a>
<ul class="sub-menu">
<li><a href="#setup-php">PHP Setup </a></li>
<li><a href="#mm-changes">Setup Instructions </a></li>
<li><a href="#quick-start">Quick Guide </a></li>
</ul>
</li>

<li>
<a href="#error1">Common errors </a>
<ul class="sub-menu">
<li><a href="#error1">No matching client found for package name</a></li>
<li><a href="#error2">Flutter SDK Not Available </a></li>
<li><a href="#error4">Authorization header not found</a></li>
<li><a href="#error5">Expired token</a></li>
<li><a href="#error6">Unsupported gradle version 7.*.*</a></li>
</ul>
</li>
<li><a href="#mm-support">Help & Support</a></li>
<li>
<a href="#mm-changelog">Change Log</a>
<ul class="sub-menu">
<li><a href="#changelog-MagicMate">MagicMate</a></li>
</ul>
</li>
<li><a href="#thank-you">Thank You</a></li>
</ul>
</nav>
</div>
<footer>
Copyright &copy;<a target="_blank" href=""> CSCODETECH</a> 2023
</footer>
</div>
<div class="wrapper-right">
<section id="app-intro">
<h3><b>Introduction to MagicMate</b></h3>
<hr>
<p>MagicMate sounds like a comprehensive app that helps with event management, organization, and planning. As a virtual assistant, Provide some general information about event management apps and their features.</p>
<p>
Flutter is a popular framework for building mobile apps, and it's great to hear that MagicMate offers a full solution app using this technology. This likely means that the app is cross-platform compatible, meaning it can be used on both iOS and Android devices, which is a huge advantage for event organizers who want to reach a wider audience.</p>
<p>
Overall, an event management app like MagicMate can be incredibly useful for anyone who regularly plans events, from small gatherings to large conferences. The convenience of having all the necessary information and tools in one place can save time and reduce stress, making the planning process more enjoyable and successful.</p>

<p>It comes with 10000+ hours save time for custome app development. It works great with both android and iOS.</p>

<p>The same code is used for iOS and Android</p>

<h5>Prerequisite</h5>
<div class="version">
<p><b>Android Studio: <span style="color: #FF0000;">Android Studio Flamingo | 2022.2.1 Patch 2</span></b></p>
<p><b>Flutter: <span style="color: #FF0000;">3.16.5</span></b> </p>
<p><b>Dart version: <span style="color: #FF0000;">3.2.3</span></b> </p>
</div>
<br>

</section>

<section id="intro">
<h3><b>Introduction to Flutter</b></h3>
<hr>
<p>Flutter is Google’s UI toolkit for building beautiful, natively compiled
applications for <a href="https://flutter.dev/docs">mobile</a>, <a href="https://flutter.dev/web">web</a>, and <a href="https://flutter.dev/desktop">desktop</a> from a
single codebase. It is
very
easy to learn and currently it is getting more and more popular. With this blog
post, you will learn some basic stuff about Flutter and after reading it, you
will be able to create a simple application using this technology.
</p>
<p><a href="https://flutter.dev/">Click here</a> to check out more details about
flutter.
</p>
</section>

<section id="tools">
<h3><b>Tools & Setup</b></h3>
<hr>
<p><strong>Prerequisite</strong></p>
<div>
<ul>
<li>Flutter &amp; Dart <a href="https://flutter.dev/docs/get-started/install" target="_blank" rel="noreferrer noopener">SDK</a></li>
<li>Anyone IDE <a href="https://developer.android.com/studio" target="_blank" rel="noreferrer noopener">Android Studio</a>
(Recommended), <a href="https://code.visualstudio.com/" target="_blank" rel="noreferrer noopener">Visual Studio Code</a> or <a href="https://www.jetbrains.com/idea/" target="_blank" rel="noreferrer noopener">IntelliJ
IDEA</a>
</li>
</ul>
</div>
<ul>
<li>To edit this project you must have Flutter and Dart installed and configured
successfully on your computer.
</li>
<li>Set up your editor - Install the<a href="https://flutter.dev/docs/get-started/editor?tab=androidstudio">
Flutter and Dart plugins</a>.
</li>
<li>
If you have got Android SDK installed and configured, to install Flutter you
only need to:
<ul>
<li>Download Flutter SDK from official website and extract it.</li>
<li>Add path to previously extracted SDK to your PATH variable</li>
<li>Run flutter doctor tool to check if everything is configured
correctly.
</li>
<li>All above steps are mentioned here: <a href="https://flutter.dev/docs/get-started/install/">https://flutter.dev/docs/get-started/install/</a>
</li>
</ul>
</li>
</ul>
</section>

<section id="setup-window">
<h3>Android Studio – Windows​</h3>
<hr>
<ul>
<li>Download Android Studio - <a href="https://developer.android.com/studio/" target="_blank" rel="noopener">https://developer.android.com/studio/</a>
</li>
<li>Get the Flutter SDK - <a href="https://flutter.dev/docs/get-started/install" target="_blank" rel="noopener">https://flutter.dev/docs/get-started/install</a></li>
<li>Learn more about Android Studio - <a href="https://developer.android.com/studio/intro/" target="_blank" rel="noopener">https://developer.android.com/studio/intro/</a></li>
</ul>
<p><strong>Step 1 : Get the Flutter SDK</strong></p>
<p> 1 Download the following installation bundle to get the latest stable release of
the Flutter SDK:
</p>
<p> 2 Extract the zip file and place the contained flutter in the desired
installation location for the Flutter SDK (for example, C:\src\flutter; do not
install Flutter in a directory like C:\Program Files\ that requires elevated
privileges).
</p>
<p><strong>Step 2 : Update your path</strong><br></p>
<p>If you wish to run Flutter commands in the regular Windows console, take these
steps to add Flutter to the PATH environment variable:
From the Start search bar, enter ‘env’ and select <b>Edit environment variables
for your account.</b>
Under <b>User variables </b>check if there is an entry called <b>Path:</b>
</p>
<ul>
<li>If the entry exists, append the full path to <b>flutter\bin </b>using ; as a
separator from existing values.
</li>
<li>If the entry doesn’t exist, create a new user variable named Path with the
full path to <b>flutter\bin</b> as its value.
</li>
</ul>
<div class="info">
<ul>
<li>
<i class="fa fa-info-circle infoText"></i><b class="infoText">Info</b>
</li>
<li>Note that you have to close and reopen any existing console windows for these
changes to take effect.
</li>
</ul>
</div>
<b>You are now ready to run Flutter commands in the Flutter Console!</b>
<p><strong>Step 3 : Run flutter doctor</strong><br></p>
<p>From a console window that has the Flutter directory in the path (see above), run
the following command to see if there are any platform dependencies you need to
complete the setup:
</p>

<p>
<pre class="lang-cpp"><code>c:\src\flutter>flutter doctor</code></pre>
</p>
<b>If you find any issue during environment setup, please go online <a href="https://flutter.dev/docs/get-started/install/windows" target="_blank" rel="noopener">Click
here</a></b>
</section>

<section id="setup-mac">
<h3><b>Android Studio – macOS​</b>
</h3>
<hr>
<ul>
<li>Download Android Studio - <a href="https://developer.android.com/studio/" target="_blank" rel="noopener">https://developer.android.com/studio/</a>
</li>
<li>Download Xcode - <a href="https://apps.apple.com/us/app/xcode/id497799835?mt=12" target="_blank" rel="noopener">https://apps.apple.com/us/app/xcode/id497799835?mt=12</a>
</li>
<li>Get the Flutter SDK - <a href="https://flutter.dev/docs/get-started/install" target="_blank" rel="noopener">https://flutter.dev/docs/get-started/install</a></li>
<li>Learn more about Android Studio - <a href="https://developer.android.com/studio/intro/" target="_blank" rel="noopener">https://developer.android.com/studio/intro/</a></li>
</ul>
<br>
<p><strong>Step 1 : Get the Flutter SDK</strong></p>
<ul>
<li>Download the following installation bundle to get the latest stable release
of the Flutter SDK:
</li>
<li>Download SDK and extract downloaded file, just double click on that. and
just copy extracted folder and paste it to your desired location (for
example, Documents\flutter).
</li>
</ul>
<br>
<p><strong>Step 2 : Update your path</strong></p>
<div class="warning">
<ul>
<li>
<i class="fa fa-bullhorn warningText"></i><b class="warningText">Warning</b>
</li>
<li>Path variable needs to be updated to access “flutter” command from terminal. you
can just update path variable for current terminal window only. and if you want
to access flutter commands from anywhere in terminal, we need to update SDK path
permanently.
</li>
</ul>
</div>
<p> To update PATH variable, we need to open terminal.
</p>
<p>To update PATH variable for current terminal window only, then enter this command
<b>"export PATH="$PATH:`pwd`/flutter/bin"" </b>and hit enter key.
</p>
<p>To update PATH variable permanently, then Open or create <b>.bash_profile</b>
file. to open or create that file, then enter <b>"sudo open -e
$HOME/.bash_profile"</b> and hit enter key.
</p>
Append below line to bash_profile file at bottom of all other content. <b>"export
PATH="$PATH:[PATH_TO_FLUTTER_GIT_DIRECTORY]/flutter/bin""</b> as
[PATH_TO_FLUTTER_GIT_DIRECTORY] is actual path of SDK folder.
<p></p>
<p>Run this command on terminal <b>"source $HOME/.bash_profile"</b> to refresh PATH
variables.
</p>
<p>Then check whether our SDK is successfully installed or not.
</p>
<p><b>You are now ready to run Flutter commands in the Flutter Console!</b>
</p>
<p>
Run <b>"flutter doctor"</b> into terminal, If you are getting check list of
flutter sdk requirements, it means SDK is successfully installed on your
machine. and you can start building flutter apps on your machine.
</p>
<p><b>If you find any issue during environment setup in macos, please go online <a href="https://flutter.dev/docs/get-started/install/macos" target="_blank" rel="noopener">Click
here</a></b>
</p>
</section>

<section id="setup-linux">
<h3><b>Android Studio – Linux​​</b></h3>
<hr>
<ul>
<li>Download Android Studio - <a href="https://developer.android.com/studio" target="_blank" rel="noopener">https://developer.android.com/studio</a>
</li>
<li>Get the Flutter SDK - <a href="https://flutter.dev/docs/get-started/install/linux" target="_blank" rel="noopener">https://flutter.dev/docs/get-started/install/linux</a>
</li>
<li>Learn more about Android Studio - <a href="https://developer.android.com/studio/intro/" target="_blank" rel="noopener">https://developer.android.com/studio/intro/</a></li>
</ul>
<p><strong>Step 1 : Get the Flutter SDK</strong></p>
<ul>
<li>Download the following installation bundle to get the latest stable release
of the Flutter SDK:
</li>
<li>Download SDK and extract downloaded file, just double click on that. and
just copy extracted folder and paste it to your desired location (for
example, Documents\flutter).
</li>
</ul>
<p><strong>Step 2 : Update your path</strong></p>
<div class="warning">
<ul>
<li>
<i class="fa fa-bullhorn warningText"></i><b class="warningText">Warning</b>
</li>
<li>Path variable needs to be updated to access “flutter” command from terminal. you
can just update path variable for current terminal window only. and if you want
to access flutter commands from anywhere in terminal, we need to update SDK path
permanently.
</li>
</ul>
</div>
<p>You’ll <code>probably</code> want to update this variable permanently, so you can
run flutter commands in any terminal session. To update PATH variable, we need
to open terminal.
</p>

<p>
<pre class="lang-cpp"><code>export PATH="$PATH:[PATH_TO_FLUTTER_GIT_DIRECTORY]/flutter/bin"</code></pre>
</p>
<ol>
<li>Run <code class="language-plaintext highlighter-rouge">source $HOME/.</code>
to refresh the current window, or open a new terminal window to
automatically source the file.
</li>
<li>Verify that the <code class="language-plaintext highlighter-rouge">flutter/bin</code>
directory is now in your PATH by running:
</li>
</ol>

<p>
<pre class="lang-cpp"><code>echo $PATH</code></pre>
</p>
<p>Verify that the <code class="language-plaintext highlighter-rouge">flutter</code>
command is available by running:
</p>

<p>
<pre class="lang-cpp"><code>which flutter</code></pre>
</p>
<b>You are now ready to run Flutter commands in the Flutter Console!</b>
</section>

<section id="build-run">
<h2 class="sub-title">Getting Started (Build & Run)
</h2>
<p></p>
<p><strong>Important</strong></p>
<p>All below steps are must be followed to build and run application
</p>
<p><strong>Download Project
</strong>
</p>
<p>Download and find the your project folder, use your preferred IDE
<strong>(Android Studio / Visual Studio Code / IntelliJ IDEA)</strong> to run
the project.
</p>
<img src="img/build_img1.png" alt="logo" width="100%">
<hr>
<p><strong>Get Dependencies
</strong>
</p>
<p>After you loaded project successfully, run the following command in the terminal
to install all the dependencies listed in the <a style="background-color: #ffffff;" href="https://dart.dev/tools/pub/pubspec"><code style="font-size: 14px;">pubspec.yaml</code></a>
file in the project's
root directory or just click on <span style="font-weight: bold;">Pub get
</span>in pubspec.yaml file<span style="font-weight: bold;"> </span>if you don't
want to use command.
</p>
<pre class="lang-cpp"><code> flutter pub get </code></pre>
<p><strong>Important</strong></p>
<p>All below steps are must be followed to build and run application</p>
<img src="img/build_img2.png" alt="logo" width="100%">
<p><strong>Build and Run App
</strong>
</p>
<div class="steps-panel">
<ol class="ordered-list">
<li>Locate the main Android Studio toolbar.</li>
 <li>In the <span style="font-weight: bolder; color: #4a4a4a;">target
selector</span><span style="color: #4a4a4a;">, select an Android
device for running the app. If none are listed as available, select
</span><span style="font-weight: bolder; color: #4a4a4a;">Tools >
Android > AVD Manager</span><span style="color: #4a4a4a;"> and
create one there. For details, see </span><a style="color: #1389fd; background-color: #ffffff;" href="https://developer.android.com/studio/run/managing-avds">Managing
AVDs</a><span style="color: #4a4a4a;">.</span>
</li>
<li>Click the run icon in the toolbar, or invoke the menu item <span style="font-weight: bolder; color: #4a4a4a;">Run > Run. </span>
</li>
</ol>
</div>
<img src="img/build_img3.png" alt="logo" width="100%">
<p>After the app build completes, you’ll see the app on your device.</p>
<p>If you don’t use Android Studio or IntelliJ you can use the command line to run
your application using the following command
</p>
<p><strong>Important</strong></p>
<p>Below step requires flutter path to be set in your Environment variables. See
https://flutter.dev/docs/get-started/install/windows
</p>
<pre class="lang-cpp"><code>flutter run</code></pre>
<p>You will see below like screen after you have build your app successfully
</p>
<img src="img/build_img4.png" alt="logo" width="100%">
<p><strong>Try hot reload</strong></p>
<p>Flutter offers a fast development cycle with Stateful Hot Reload, the ability to
reload the code of a live running app without restarting or losing app state.
Make a change to app source, tell your IDE or command-line tool that you want to
hot reload, and see the change in your simulator, emulator, or device.
</p>
<p><strong>Important</strong></p>
<p>Do not stop your app. let your app run.</p>
</section>


<section id="config-android">
<h2 id="cutomization" class="sub-title">Configuration & Customization
</h2>
<h3><b>Android Configuration</b></h3>
<div class="danger">
<ul>
<li>
<i class="fa fa-exclamation-triangle dangerText"></i><b class="dangerText">Important</b>
</li>
<li>Don't open/change android code inside flutter because flutter doesn't compile android files.
</li>
<li>If you want add/change android code, click on <br>
<b>Tools->Flutter->Open Project in Android Studio </b>Or<b> File->Open ->open project 
module inside your project</b>
</li>
</ul>
</div>
<div class="danger">
<ul>
<li>
<i class="fa fa-exclamation-triangle dangerText"></i><b class="dangerText">Important</b>
</li>
<li>Don't change package name inside <b>android/app/src/main/AndroidManifest.xml</b> file</li>
</ul>
</div>

<ul>
<li>Open Android Studio.</li>
<li>Select Open an existing Android Studio Project.
</li>
<li>Open the android directory within your app.
</li>
<li>Wait until the project has been synced successfully. (This happens
automatically once you open the project, but if it doesn’t, select Sync
Project with Gradle Files from the File menu).
</li>
<li>Now, click on Run button.</li>
</ul>
<p><strong>Change Application Name
</strong>
</p>
<ul>
<li>You must want to change your application name. This is how you can do.
Follow the below step.
</li>
<li>Open /android/app/src/main/AndroidManifest.xml and specify your application
name.
</li>
<pre class="lang-cpp"><code>
&lt;manifest xmlns:android="http://schemas.android.com/apk/res/android" package="com.example"&gt;
                    
&lt;uses-permission android:name="android.permission.INTERNET" /&gt;
&lt;uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /&gt;
                    
&lt;application
    android:name="io.flutter.app.FlutterApplication"
    android:label="YOUR_APPLICATION_NAME"
    android:icon="@mipmap/ic_launcher"&gt;
&lt;activity&gt;
                        </code>
                        </pre>
</ul>

<p><strong>Change Application Icon
</strong>
</p>
<ul>
<p>See How to generate an application icon?
</li>
<li><a href="https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html">Browse your image</a> and click on Download icon. After successfully generated,
replace all icons in respective folders:
</li>
</ul>
<ul>
<li>/mipmap-hdpi in /android/app/src/main/res/ folder</li>
<li>/mipmap-mdpi in /android/app/src/main/res/ folder</li>
<li>/mipmap-xhdpi in /android/app/src/main/res/ folderr</li>
<li>/mipmap-xxhdpi in /android/app/src/main/res/ folder</li>
<li>/mipmap-xxxhdpi in /android/app/src/main/res/ folder</li>
</ul>
<div class="warning">
<ul>
<li>
<i class="fa fa-bullhorn warningText"></i><b class="warningText">Warning</b>
</li>
<li>Application icon name must be ic_launcher</li>
</ul>
</div>
<p><strong>Change Application ID
</strong>
</p>
<ul>
<li>Follow the below steps to change you Application ID.
</li>
<li>Open /android/app/build.gradle
</li>
<div class="row">
<div class="col-md-12">
<pre class="lang-cpp"><code>
defaultConfig {
   applicationId "YOUR_APPLICATION_ID"
   minSdkVersion 21
   targetSdkVersion 31
   versionCode 1
   versionName "1.0.0"
   testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
}
            </code>
            </pre>
</div>
</div>

<p><strong>Generate Signed APK</strong></p>
<ul>
<li>Go to in your project & then Tools -> Flutter -> Open for Editing in Android Studio as shown below </li>
<br>
<img src="img/apk_step1.png" alt="logo" width="100%"><br><br>
<li>If your 1st step option is not available then select File -> open -> Select main folder -> android.</li>
<li>Open Project it in New Window</li>
<li>Wait for while until project synchronization. After that Go to Build -> GenerateSigned Bundle/APK...</li>
<br>
<img src="img/apk_step2.png" alt="logo" width="100%"><br><br>
<li>Select Android App Bundle or APK Option as per your need. (Android App Bundle is best solution) and click Next button.</li>
<br>
<img src="img/apk_step3.png" alt="logo" width="100%"><br><br>
<li>Select Create new.. option to generate new Signed key (When you release your app First Time) and Fill all options. <a href="https://developer.android.com/studio/publish/app-signing#generate-key">Refer this link </a></li>
<br>
<img src="img/apk_step4.png" alt="logo" width="100%"><br><br>
<li>Click Next button and you will get following screen...</li>
<br>
<img src="img/apk_step5.png" alt="logo" width="100%"><br>
<p>Select Build variants - release and Signature versions both V1 and V2 respectively as shown above screen shot and click Finish button.</p>
<li>Wait for a while until Gradle Build Running process complete.and finally you will get the Generate Signed APK : (APKs) generated successfully . from that click on Locate option to get Location of your Generate Signed APK Key.</li>
</ul>
</ul>

</section>

<section id="config-ios">
<h3 class="block-title"><b>iOS Configuration</b>
</h3>
<hr>
<p><strong>Open Project in Android Studio
</strong>
</p>
<ul>
<li>Open android studio in your project.</li>
<li>Open terminal in android studio.</li>
<li>Pub get.</li>
<li>Open terminal cd ios.</li>
<li>pod install.</li>
<li>run project in xcode.</li>
</li>
</ul>
<p><strong>Open Project in Xcode
</strong>
</p>
<ul>
<li>
Open Xcode.
</li>
<li>Select Open another Project.</li>
<li>Open the iOS directory within your app.</li>
<li>Now, click on Done button.</li>
</li>
</ul>
<p><strong>Change Bundle Name
</strong>
</p>
<ul>
<li>
Select your project file icon in Group and files panel.
</li>
<li>Then Select Target -> Info Tab.</li>
<li>At last change Bundle Name.</li>
</li>
</ul>
<img src="img/ios_config_img1.png" alt="logo" width="100%">

<p><strong>Change Bundle Identifier.
</strong>
</p>
<p>Bundle Id is a unique Identifier of your of app on iOS and MacOS. iOS and MacOS
use it to recognise updates to your app. The identifier must be unique for your
app.
</p>
<ul>
<li>
Select your project file icon in Group and files panel.
</li>
<li>Select General Tab.</li>
<li>After Select General tab you can see the details of your application.</li>
<li>In Identity section, rename your Bundle identifier.</li>
</li>
</ul>
<img src="img/ios_config_img2.png" alt="logo" width="100%">
<p><strong>Change App Icons</strong></p>
<ul>
<li>see How to Generate App Icons?
</li>
<li>In Group and files panel find “Assets.xcassets” folder.
</li>
<li>In Assets.xcassets folder replace AppIcon.
</li>
</ul>

</section>

<section id="config-flutter">
<h2>Flutter Configuration</h2>
<div class="content">
<h5>Manually Change App Name</h5>
<p>In main directory Go to app -> src -> main -> AndroidManifest.xml </p>
<p>Change YOUR_APP_NAME to your app name </p>
<pre class="lang-cpp"><code>Androi:label=""
                </code></pre>

</section>
<section id="setup-php">
<h3><b>PHP Setup</b></h3>
<hr>
<h4>Requirements</h4>
<div class="danger">
<ul>
    <li>
<i class="fa fa-exclamation-triangle dangerText"></i><b class="dangerText">Important</b>
</li>
 <li> Required Admin Panel </li>
    <li> Required Subdomain configuration for the setup panel</li>

</ul>
</div>
<ul>
<li>PHP &gt;= 8.0</li>
<li>MySQL &gt;= 5.6</li>
<li>Apache HTTP server</li>
<li>OpenSSL PHP Extension</li>
<li>Enable Output_buffering in Php.ini File</li>
</ul>
</section>
<section id="mm-changes">
<h4>Installation Instructions</h4>
<ul>
<li><b>Make Sub domain</b></li>
<li>subdomain.yourdomain.com</li>
<li>Unzip the MagicMate Admin Panel 1.0.zip file and ONLY use the extracted /root folder and upload in below
directory based on your server</li>
<li><b>In Linux</b></li>
<li>Path: var/www/html/</li>
<li><b>In cPanel:</b></li>
<li>Inside File manager -&gt; Path: public_html/</li>
<li>Create the database on your server</li>
<li>Import MagicMate 1.0.sql file in created database. ( <b>Note</b> : Only necessary for those client, who
is setup project first time. )</li>
<div class="version">
<h5>Already imported MagicMate 1.0.sql</h5>
</div>
</ul>
<h4>Admin Default credentials</h4>
<div class="row">
<div class="col-md-12">
<pre class=" language-cpp"><code class=" language-cpp">
Email    : admin
Password : admin@123
</code>
</pre>
</div>
</div>

<h4>1. Change Database settings</h4>
<p>For database settings, open the filemanager/evconfing.php file with a text editor and set your
database settings.</p>
<p>Enter database name, database username, database password and the host details</p>
<img src="img/db_connection.png" alt="logo" width="100%">

<h4>2. Change Map Key In Add Event Page</h4>
<p>For Google Map settings, open the add_event.php file with a text editor and set your
Map Key settings.</p>
<p>Replace Your Key With Below Both Screenshort. Replace Your Google Map Key With this String Map_Key_Here. </p>
<img src="img/googlemap1.png" alt="logo" width="100%">





</section>

<section id="quick-start">
    <h4>1. Quick Start Admin Menu Guide</h4>
    <ul>
<li>1.<strong> User Registration and Login:</strong> Users can register for an account with OTP and log in using their mobile number.</li>
<li>2.<strong> Create and Manage Events:</strong> Event Organizer can create events with details such as event name, date, time, location, and description. They can also manage and update their events.</li>
<li>3.<strong> Event Discovery:</strong> Users can discover events based on their location, interests, and categories.</li>
<li>4.<strong> Ticket Booking:</strong> Users can book tickets for events directly within the app. Ticket prices, availability, and payment methods are displayed clearly.</li>
<li>5.<strong> Payment Integration:</strong> The app supports multiple payment methods and mobile wallets.</li>
<li>6.<strong> Push Notifications:</strong> Users receive push notifications for important updates related to their events, ticket booking, and more.</li>
<li>7.<strong> Event Analytics:</strong> Event organizers can track event attendees, ticket sales, revenue, and other key metrics to optimize their events.</li>
<li>8.<strong> Reviews and Ratings:</strong> Users can leave reviews and ratings for events they attend, helping other users make informed decisions.</li>
<li>9.<strong> Multi-Language Support:</strong> The app supports multiple languages, making it accessible to a broader audience.</li>
<li>10.<strong> Admin Dashboard:</strong> The app includes an admin dashboard that allows admins to manage user accounts, events, payments, and more.</li>
<li>11.<strong> QR Code Check-In:</strong> Event organizers can use QR codes to check in attendees, reducing wait times and improving the overall event experience.</li>
<li>12.<strong> Real-Time Updates:</strong> The app provides real-time updates on events, including changes to event details and cancellations.</li>

    </ul>
    <img src="img/dashboard.png" alt="logo" width="100%">

   
</section>
<section id="baseurl-flutter">
    <h2>Base URL Configuration</h2>
<div class="content">
<div class="danger">
<ul>
<li>
<i class="fa fa-exclamation-triangle dangerText"></i><b class="dangerText">Important</b>
</li>
<li>Please do not change anything in the flutter code. If you use PHP backend you only need to
set the base URL otherwise set only the puchase code.
</li>
</ul>
</div>
<h4>With PHP Backend</h4>
<h5>Change Base URL, Add google Map API & Onesignal App ID For 2 Apps</h5>
<p>In Main directory goto the utils folder and open constant.dart file and change your BASE_URL value.
</p>
<pre class=" language-cpp"><code class=" language-cpp">const BASE_URL="ADD YOUR BASE URL/user_api";</code></pre>
<pre class=" language-cpp"><code class=" language-cpp">const BASE_URL="ADD YOUR BASE URL/orag_api";</code></pre>
<img src="img/baseurl.png" alt="logo" width="100%">
<h5>How to get Base URL</h5>
<p>Open your Admin login page and copy the URL.</p>
<img src="img/admin_top.png" alt="logo" width="100%">
<h5>How to Change Google MAP API</h5>
<p>Open your project and add it here.</p>
<img src="img/googlemap.png" alt="logo" width="100%">
</div>
</section>
<section class="section" id="mm-firebase" >
<h3 class="block-title"><b>Firebase Configuration</b>
</h3>
<hr>
<p><strong>Create a Firebase project
</strong></p>
<p>Before you can add Firebase to your Flutter app, you need to create a Firebase
project to connect to your app. Visit <a style="margin: 0px; padding: 0px; border: 0px; transition-duration: 0.4s;" href="https://firebase.google.com/docs/projects/learn-more" target="_blank" rel="noopener">Understand Firebase Projects</a> to learn more about Firebase
projects.</p>
<div class="warning">
<ul>
<li>
<i class="fa fa-bullhorn warningText"></i><b class="warningText">Warning</b>
</li>
<li>If you're releasing your Flutter app on both iOS and Android, register both the
iOS and Android versions of your app with the same Firebase project.</li>
</ul>
</div>
<p>Visit for more information on <a href="https://firebase.google.com/docs/flutter/setup?platform=android" target="_blank" rel="noopener">how to setup for Android</a></p>
<p>Visit for more information on <a href="https://firebase.google.com/docs/flutter/setup?platform=ios" target="_blank" rel="noopener">how to setup for iOS</a></p>
<p><strong>Register your app with Firebase
</strong></p>
<ul>
<li>In the center of the <a href="https://console.firebase.google.com/?pli=1" target="_blank" rel="noopener">Firebase console's project overview
page</a>, click the Android icon to launch the setup workflow.</li>
<li>Enter your app's package name in the Android package name field.</li>
<br>
<div class="warning">
<ul>
<li>
<i class="fa fa-bullhorn warningText"></i><b class="warningText">Warning</b>
</li>
<li>Make sure that you enter the ID that your app is actually using. You cannot
add or modify this value after you register your app with your Firebase
project. Both applicationId should be matched.</li>
</ul>
</div>
<li>Click Register app.
</li>
</ul>
<ul>

                                    <li>If you haven’t Firebase Project Account ? Click here for create projet <a
                                            href="https://console.firebase.google.com/">https://console.firebase.google.com/</a>
                                    </li>
                                    <li><img alt="" width="1000px"
                                            style="margin-top: 25px;margin-bottom: 15px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;"
                                            src="img/new-step/firebase1.png"></li>
                                    <li><img alt="" width="1000px"
                                            style="margin-top: 25px;margin-bottom: 15px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;"
                                            src="img/new-step/firebase2.png"></li>
                                    <li><img alt="" width="1000px"
                                            style="margin-top: 25px;margin-bottom: 15px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;"
                                            src="img/new-step/firebase3.png"></li>
                                    <li><img alt="" width="1000px"
                                            style="margin-top: 25px;margin-bottom: 15px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;"
                                            src="img/new-step/firebase4.png"></li>
                                </ul>

                                <h4 class="mt-4">Android :</h4>
                                <hr>

                                <ol>
                                    <li>Select an Android app in the firebase console.</li>

                                    <img alt="" width="1000px"
                                        style="margin-top: 25px;margin-bottom: 25px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;"
                                        src="img/new-step/android1.png">
                                    <li>Write project package name and SHA-1 Key.</li>
                                    <img alt="" width="1000px"
                                        style="margin-top: 25px;margin-bottom: 25px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;"
                                        src="img/new-step/android2.png">
                                    <li>Download google-service.json file and keep in project root directory
                                    </li>
                                    <img alt="" width="1000px"
                                        style="margin-top: 25px;margin-bottom: 25px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;"
                                        src="img/new-step/android3.png">
                                    <li>Example MagicMate(Project Name) -&gt; android -&gt; app </li>
                                    <li>Replace the file in "android/app"</li>
                                    <img alt="" width="1000px"
                                        style="margin-top: 25px;margin-bottom: 25px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;"
                                        src="img/new-step/android6.png">


                                    

                                </ol>


                                <h4 class="mt-4">iOS:</h4>
                                <hr>
                                <ol>
                                    <li>Create an IOS or Android click on Add app</li>
                                    <img alt="" width="1000px"
                                        style="margin-top: 25px;margin-bottom: 25px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;"
                                        src="img/new-step/ios1.png">
                                    <li>Select an iOS app in the firebase console.</li>
                                    <img alt="" width="1000px"
                                        style="margin-top: 25px;margin-bottom: 25px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;"
                                        src="img/new-step/ios2.png">
                                    <li>Write bundlid and APPID</li>
                                    <img alt="" width="1000px"
                                        style="margin-top: 25px;margin-bottom: 25px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;"
                                        src="img/new-step/ios3.png">
                                    <li>Download the Googleservice-info.plist file</li>
                                    <img alt="" width="1000px"
                                        style="margin-top: 25px;margin-bottom: 25px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;"
                                        src="img/new-step/ios4.png">
                                    <li>Replace the file in "ios/Runner/info.plist"</li>
                                    <li>Update "FirebaseApp.Configure()" in AppDelegate File</li>
                                    <img alt="" width="1000px"
                                        style="margin-top: 25px;margin-bottom: 25px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;"
                                        src="img/new-step/ios5.png">
                                </ol>
                                

                    <div class="col-sm-12">
                        <div class="card" id="features">
                            <div class="card-header">
                                <h5>How to add SHA-1 key in firebase</h5>
                            </div>
                            <div class="card-body p-t-0">
                                <ol>
                                    <li>On left hand side panel click on setting <i class="fa fa-gear"> icon. On click
                                            Setting
                                            button pop open open. In that click on <b>Project Settings</b></i></li>
                                    <img alt="" width="1000px" style="margin-top: 15px;" src="img/new-step/sha1.png"><br>


                                    <li>In General tab, go down in that click <b>Add fingerprint</b></li>
                                    <img alt="" width="1000px" style="margin-top: 15px;" src="img/new-step/sha2.png"><br>

                                    <li>Generate debug and release SHA1 and SHA26 key and in fingerprint</li>
                                    <img alt="" width="1000px" style="margin-top: 15px;" src="img/new-step/sha3.png"><br>
                                    <li>Add you're firebase Project ID</li>
                                    <img alt="" width="1000px" style="margin-top: 15px;" src="img/new-step/firebase-notification.png"><br>
                                </ol>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12">
                        <div class="card" id="features">
                            <div class="card-header">
                                <h5>Create Debug and Release SHA-1 add in firebase</h5>
                            </div>
                            <div class="card-body p-t-0">
                                <ul>
                                    <li><b>For Debug mode:</b>
                                        <ul>
                                            <li>keytool -list -v -keystore ~/.android/debug.keystore -alias
                                                androiddebugkey</li>
                                            <li>-storepass android -keypass android</li>
                                        </ul>
                                    </li>
                                </ul>
                                <ul>
                                    <li><b>for Release mode:</b>
                                        <ul>
                                            <li>keytool -list -v -keystore [keystore_name] -alias [alias_name].</li>
                                        </ul>
                                    </li>
                                </ul>
                                <ul>
                                    <li><b>For Create Bundle in android :</b>
                                        <ul>
                                            <li>If you want to create bundle instead of apk you have to add new signin
                                                SHA-1 From
                                                you google console like :
                                                <br>
                                                Go to Google play console ⇒ Select you app ⇒ Go to release Management ⇒
                                                Open App
                                                Signin ⇒ Copy SHA-1 from app singin certificated and paste it to
                                                firebase project
                                                settings.
                                            </li>
                                            <img alt="" width="1000px"
                                                style="margin-top: 25px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;"
                                                src="img/new-step/4.png">
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
</section>
<section class="section" id="mm-firebase-rules" >
    <div class="col-sm-12">
                        <div class="card" id="features">
                            
                            <div class="card-body p-t-0">
                                
                               

                                <h4><b>Firebase Rules:</b></h4>
                                <hr>
                                 <p>For save data in Firebase storage and upload file,images,videos etc need to give
                                    permission</p>
                                <ol style="list-style: decimal;letter-spacing: 0px;">
                                    <li> On left hand side panel,go to build, select Firestore database.
                                    </li>

                                    <li>Click on <b>Create Database</b>.
                                        <br> <img src="img/firebase-rule/rule1.png" alt="" width="1000px"
                                            style="margin-top: 25px;margin-bottom: 35px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;">
                                    </li>
                                    <li>Then select mode wheather for live or debug mode need to use
                                        <br> <img src="img/firebase-rule/rule2.png" alt="" width="1000px"
                                            style="margin-top: 25px;margin-bottom: 35px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;">
                                    </li>

                                    <li>Now click <b>Rules tab</b>
                                        <br> <img src="img/firebase-rule/rule3.png" alt="" width="1000px"
                                            style="margin-top: 25px;margin-bottom: 35px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;">
                                    </li>
                                    <li>Change condition by true</li>

                                    <li>After doing true click on publish button so that you can access database. <br>
                                        <img src="img/firebase-rule/rule4.png" alt="" width="1000px"
                                            style="margin-top: 25px;margin-bottom: 35px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;">
                                    </li>
                                </ol>

                                <h4 class="mt-4">For store Images, Videos, Audios, etc.:</h4>
                                <hr>

                                <ol style="list-style: decimal;letter-spacing: 0px;">
                                    <li> On left hand side panel,go to build, select <b>Storage</b>.
                                    </li>

                                    <li>Click on <b>Get started</b>.
                                        <br> <img src="img/firebase-rule/rule5.png" alt="" width="1000px"
                                            style="margin-top: 25px;margin-bottom: 35px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;">
                                    </li>
                                    <li>Then select mode wheather for live or debug mode need to use
                                        <br> <img src="img/firebase-rule/rule6.png" alt="" width="1000px"
                                            style="margin-top: 25px;margin-bottom: 35px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;">
                                    </li>

                                    <li>Now click <b>Rules tab</b>
                                        <br> <img src="img/firebase-rule/rule3.png" alt="" width="1000px"
                                            style="margin-top: 25px;margin-bottom: 35px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;">
                                    </li>
                                    <li>Change condition by true</li>

                                    <li> After doing true click on publish button so that you can access database.
                                        <img src="img/firebase-rule/rule7.png" alt="" width="1000px"
                                            style="margin-top: 25px;margin-bottom: 35px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;">
                                    </li>
                                </ol>
                                <h4><b>Firebase Collection:</b></h4>
                                <hr>
                                <p>As you have know there are multiple collection has been used in firebase. Let's see what is the use of the collection and how it added<br>


Note: If any error occur regarding QuerySnapshot then check in firebase if missing any below Required config is missing if not then check there field and datatype and key word same to same as given in document</p>
<img src="img/firebase-rule/rule8.png" alt="" width="1000px"
                                            style="margin-top: 25px;margin-bottom: 35px;box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;border-radius: 5px;">
                                            <div class="warning">
<ul>
<li>
<i class="fa fa-bullhorn warningText"></i><b class="warningText"> NOTE :</b>
</li>
<li>In above collection list [Auto generate] means this collections are generate in firebase whenever any first user enter any data . For other collections need to add manually in firebase. See the below 2 options for add collection data in firebase.
</li>
</ul>
</div>
<h4><b>Firebase Cloud Messaging API (HTTP v1)</b></h4>
                                <hr>
                                <div class="card-header">
                                    
                                    <ol style="font-size: medium;">
                                        <li>1. Create A Firebase Project<br>
                                            <img src="img/cloud-messaging/step1.jpg" alt="" width="1000px" style="margin-top: 25px; margin-bottom: 35px; box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; border-radius: 5px">
                                        </li>
                                        <li>2. Now go to your Firebase project, On left hand side panel click on setting
                                            icon. On click Setting button pop open open. In that click on Project
                                            Settings<br>
                                             <img src="img/cloud-messaging/step3.jpg" alt="" width="1000px" style="margin-top: 25px; margin-bottom: 35px; box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; border-radius: 5px">
                                        </li>
                                        <li>3. On the subsequent page, click Enable. You may need to wait a few minutes for the action to propagate to Firebase systems.<br>
                                             <img src="img/cloud-messaging/step4.jpg" alt="" width="1000px" style="margin-top: 25px; margin-bottom: 35px; box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; border-radius: 5px">
                                        </li>
                                        <li>4. Generate a Private Key JSON file<br>In Project settings, go to the Service accounts tab.<br>Click Generate new private key at the bottom of the page.<br>
                                             <img src="img/cloud-messaging/step5.jpg" alt="" width="1000px" style="margin-top: 25px; margin-bottom: 35px; box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; border-radius: 5px">
                                        </li>
                                        <li>5. You'll then see a warning window. Click Generate key.<br>Save the JSON file somewhere secure. You will need to access it shortly.<br>
                                             <img src="img/cloud-messaging/step6.jpg" alt="" width="1000px" style="margin-top: 25px; margin-bottom: 35px; box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; border-radius: 5px">
                                        </li>
                                        <li>6. You received you'reprojectname.json file, just open in any editor and copy all the content and paste to the firebase_accesstoken.dart<br>
                                             <img src="img/cloud-messaging/step-file.png" alt="" width="1000px" style="margin-top: 25px; margin-bottom: 35px; box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; border-radius: 5px">
                                        </li>
                                        <li>7. Open the firebase_options.dart and add all the details from google-service.json you received from Firebase<br>
                                             <img src="img/cloud-messaging/firebase-config.png" alt="" width="1000px" style="margin-top: 25px; margin-bottom: 35px; box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; border-radius: 5px">
                                        </li>
                                        
                    <br>
                   
                                    </ol>
                            </div>
                        </div>
                    </div>
              
</section>
<section class="section" id="mm-firebase-recaptcha" >
    <div class="card" id="features">
                            
                            
                             <div class="card-header">
                              <h4><b>Twillo Configuration</b></h4>
                                <hr>
                            </div>
                            <p>Follow this link <a href="https://help.twilio.com/articles/14726256820123">https://help.twilio.com/articles/14726256820123</a></p>
                            <div class="warning">
<ul>
<li>
<i class="fa fa-bullhorn warningText"></i><b class="warningText"> NOTE :</b>
</li>
<li>Open Admin -> Goto Setting page -> Scroll Down and Add key
</li>
</ul>
</div>
                            <div class="card-header">
                              <h4><b>MSG91 Configuration</b></h4>
                                <hr>
                            </div>
                            <p>Follow this link <a href="https://msg91.com/help/where-can-i-find-my-authentication-key">https://msg91.com/help/where-can-i-find-my-authentication-key</a></p>
                            <p>Follow this link <a href="https://msg91.com/help/where-to-find-my-flow-id">https://msg91.com/help/where-to-find-my-flow-id</a></p>
                             <div class="warning">
<ul>
<li>
<i class="fa fa-bullhorn warningText"></i><b class="warningText"> NOTE :</b>
</li>
<li>Open Admin -> Goto Setting page -> Scroll Down and Add key
</li>
</ul>
</div>
<div class="card-header">
                              <h4><b>Add Google Map API Key</b></h4>
                                <hr>
                            </div>
                            <ul><li>1. You need to generate the google API key. Visit this link -
<a href="https://developers.google.com/maps/documentation/embed/get-api-key" target="_blank" rel="noopener noreferrer">https://developers.google.com/maps/documentation/embed/get-api-key</a></li><li>2. You need to enabled mentione APIs: Direction API, Distance Matrix API, Geocoding API, Maps SDK for Android, Maps SDK
for iOS, Place API.</li><li>3. You have to enable billing account. Visit this url for
activating: <a href="https://support.google.com/googleapi/answer/6158867?hl=en" target="_blank" rel="noopener noreferrer">https://support.google.com/googleapi/answer/6158867?hl=en</a></li><li>4. After generating API key, you have to put it on 3 different place for Android, iOS and web.</li></ul>
<div class="warning">
<ul>
<li>
<i class="fa fa-bullhorn warningText"></i><b class="warningText"> NOTE :</b>
</li>
   
    <li>Android: App -> src -> main -> AndroidManifest.xml  -> android:name="com.google.android.geo.API_KEY" android:value=“YOUR_MAP_API_KEY_HERE”/>
</li>
    <li>iOS: <project>/iOS/Runner/AppDelegate.swift Add it after Bool { iOS: GMSServices.provideAPIKey("YOUR KEY HERE")</li>
</li>
</ul>
</div>

              
</section>
<section id="mm-onesignal">
<h3 class="block-title"><b>OneSignal Configuration</b>
</h3>

<p><strong>Create a OneSignal Account and Make 1 different projects inside</strong></p>
<hr>
<p> One Signal is used for send the push notification into mobile or web. for more detail about
<a href="https://documentation.onesignal.com/docs/flutter-sdk-setup">OneSignal Documentation.</a>
</p>
<p>if you have own OneSignal then login and create project. if you does not then register account and
create project</p>
<p>Follow below steps</p>
<ul>
<li>Click in to New App/Website button.</li>
<li>Enter the App/website name in AppName Field.</li>
<li>Select one platform configure, for Example(iOS,Android,Web push).</li>
<li>After Select Any One platform to configure. we can get Firebase Server Key and Firebase Sender
Id. If you can select iOS platform then you can required the production Push Certificate.(.p12
Certificate) and then get Firebase server Key. Then Click on Save Button.</li>
<li>After get Firebase Server key and Sender ID. Goto the firebase app and in left
side you see Project overview and settings click on it and select Project Settings. and In
Setting. select Cloud messaging. and Enter Server Key and Sender ID and at the end Click into
Save Button.</li>
</ul>
<p><strong>Step 1: Create onesignal project in your account Choose android platform
</strong></p>
<img src="img/o1.png">
<p><strong>Step 2: Click on Service Account JSON > Choose file and select the JSON file you downloaded from your service account. (Firebase Cloud Messaging API (HTTP v1) Step 4)
</strong></p>
<img src="img/json-upload.jpg">
<p><strong>Step 3: Choose Target SDK as Flutter
</strong></p>
<img src="img/o4.png">

<p><strong>Step 4: Now copy App ID as shown below
</strong></p>
<img src="img/o5.png">
<p><strong>Step 5: Paste this App ID here
</strong>
<pre class=" language-cpp"><code class=" language-cpp">static const String oneSignel ="ADD YOUR APP ID";
                        </code></pre>
</p>
<img src="img/onesignal.png">
<p><strong>Step 6: If you didn't have copy app id before then you can get Onesignal App ID from this settings here and paste it to admin setting tab
</strong></p>
<img src="img/o6.png">
<p><strong>Step 7:  Onesignal App ID & Rest Api Key paste it to admin setting tab </span>
</strong></p>
<img src="img/o7.png">
</section>
<section id="changelog">
<h2>v1.5(20 July 2024)</h2>
<div class="content">
<ul>
<li><a href="https://1.envato.market/DKkodn">Check Here</a></li>
</ul>
</div>
</section>

<section id="error1">
<h2>No matching client found for package name </h2>
<div class="content">
<ul>
<li>The error is "package_name" in google-services.json is not matching with your "applicationId" in
app gradle.
</li>
<li>Just make sure the package_name and applicationId both are same.</li>
</ul>
</div>
</section>
<section id="error2">
<h2>Flutter SDK Not Available </h2>
<div class="content">
<p>Download the SDK and point the SDK folder path in your future projects.</p>
<p> There are different sources you can try</p>
<ul>
<li>You can clone it from the <a href="https://github.com/flutter/flutter">Github Repository</a>
</li>
<li>
<a href="https://flutter.dev/docs/get-started/install/macos#get-the-flutter-sdk">
Download SDK
zip file + extract it after downloading
</li>
<li>You can also Download any <a href="https://flutter.dev/docs/development/tools/sdk/releases?tab=macos">version(including
older)</a> from here (For Mac, Windows, Linux)</li>
</ul>
</div>
</section>
<section id="error4">
<h2>
Authorization header not found
</h2>
<div class="content">
<p>Message: Authorization header not found
</p>
<p><b>Note: </b>If you faced this error during development then this error due to header not set on your
admin panel
</p>
<p>Solution: Just check <a href="#basic-Wordpress">Enable JWT Authentication</a> section wordpress document to resolve this issue</p>
</div>
</section>
<section id="error5">
<h3 class="block-title">
<b>
Expired token
</b>
</h3>
<hr>
<div class="section-block">
<h4>Message: Expired token</h4>
This error occurs due to the Expired user token.
<h5>Solution: Logout and re-login to your flutter app</h5>
</div>
</section>
<section id="error6">
<h3 class="block-title">
<b>
Unsupported gradle version 7.*.*
</b>
</h3>
<hr>
<div class="section-block">
<h4>Message: Unsupported gradle version 7.*.*</h4>
<h6>Solution:</h6>
<p>1. Open project_root_directory/android in Android Studio</p>
<p>2. Wait for indexing</p>
<p>3. Now run your application from android module</p>
</div>
</section>


<section id="mm-support">
<h2>Help & Support </h2>
<div class="content">
<p>We like to hear you out when you get stuck or encounter difficulty with our products. As soon as you buy one of our products – you can open a support ticket and share your concern right away.
Skype<a href=""> CSCODETECH</a> or email: <EMAIL>
</p>
<br>
<b>Support Policy:</b>
<p>
It is recommended that before submitting a ticket you take a close look at product documentation (Docs folder in the
archive you have downloaded from Themeforest/Codecanyon). To get technical support and assistance,
you need to have a valid purchase code. You will find this when you SignIn your Codecanyon/Themeforest “Download” page.
Click on the product’s download link.
</p>
<h5 style="color: red;">Please Note:</h5>
<p>
Free support policy includes troubleshooting, technical assistance with the product only.
It does not include any customization, additional features integration or concerns about third-party plugins compatibility.
But, support is applied to plugin(s) we have developed and integrated ourselves. We appreciate your understanding!</b>
</p>
<p>
You can expect answer within 24-48 hours, usually as soon as possible in the order they were received.
</p>

<p>
All support requests are being processed on business days (Monday to Friday) from 10:00 to 18:00 (IST  +05.30). We are in IST +5:30 time zone.
We address all the support queries 5 days weekly on the first-come, first-solve basis (Saturday, Sundays off).
</p>
<p>
We like getting positive feedback from our customers, and this is why we do our best to earn it! Write a review: <a href="https://codecanyon.net/downloads">https://codecanyon.net/downloads</a>
</p>
</div>
</section>


<section id="changelog-MagicMate">
<h2>Change Log</h2>
<div class="content">
<strong>Version 1.0 - 20 July 2024</strong>
<ul>
<li>Initial Release</li>
</ul>
</div>
</section>


<section id="thank-you">
<h2>Thank you for purchasing our application</h2>
<div class="document-content">
<p>
Thank you for purchasing our application! We're glad that you found what you were looking for. It is our goal that you are always happy with what you bought from us, so please let us know if your buying experience was anything short of excellent. We look forward to seeing you again.
</p>
<p><a href="https://codecanyon.net/user/cscode_tech/follow"><img src="http://cscodetech.com/mockup/profile-envato.png"/></a><a href="https://www.facebook.com/cscodetech/"><img src="http://cscodetech.com/mockup/profile-fb.png"/></a><a href="https://cscodetech.com/portfolio"><img src="http://cscodetech.com/mockup/profile-portfolio.png"/></a><a href="https://cscodetech.com/hello/"><img src="http://cscodetech.com/mockup/profile-support.png"/></a></p>
</div>
</section>

</div>
</div>
</body>

</html>