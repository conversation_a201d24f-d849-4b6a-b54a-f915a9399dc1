import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:magicmate/model/hotel_model.dart';
import 'package:magicmate/model/room_model.dart';
import 'package:magicmate/model/booking_model.dart';
import 'package:magicmate/firebase/merged_app_service.dart';

class HotelController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Observable lists
  var hotels = <Hotel>[].obs;
  var popularHotels = <Hotel>[].obs;
  var favoriteHotels = <Hotel>[].obs;
  var rooms = <Room>[].obs;
  var userBookings = <Booking>[].obs;

  // Loading states
  var isLoading = false.obs;
  var isLoadingRooms = false.obs;
  var isLoadingBookings = false.obs;

  // Selected items
  var selectedHotel = Rxn<Hotel>();
  var selectedRoom = Rxn<Room>();

  // Error handling
  var error = Rxn<String>();

  @override
  void onInit() {
    super.onInit();
    fetchHotels();
    fetchPopularHotels();
  }

  // Fetch all hotels using merged Firebase service
  Future<void> fetchHotels() async {
    try {
      isLoading.value = true;
      error.value = null;

      // Use merged Firebase service for better integration
      hotels.value = await MergedAppFirebaseService.fetchHotels();

      debugPrint('HotelController: Fetched ${hotels.length} hotels');
    } catch (e) {
      error.value = e.toString();
      debugPrint('HotelController: Error fetching hotels: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Fetch popular hotels
  Future<void> fetchPopularHotels() async {
    try {
      final querySnapshot = await _firestore
          .collection('hotels')
          .where('status', isEqualTo: 'active')
          .orderBy('rating', descending: true)
          .limit(10)
          .get();

      popularHotels.value =
          querySnapshot.docs.map((doc) => Hotel.fromFirestore(doc)).toList();

      debugPrint(
          'HotelController: Fetched ${popularHotels.length} popular hotels');
    } catch (e) {
      debugPrint('HotelController: Error fetching popular hotels: $e');
    }
  }

  // Search hotels
  Future<List<Hotel>> searchHotels(String query) async {
    try {
      if (query.isEmpty) return hotels;

      final querySnapshot = await _firestore
          .collection('hotels')
          .where('status', isEqualTo: 'active')
          .get();

      final searchResults = querySnapshot.docs
          .map((doc) => Hotel.fromFirestore(doc))
          .where((hotel) =>
              hotel.name.toLowerCase().contains(query.toLowerCase()) ||
              hotel.city.toLowerCase().contains(query.toLowerCase()) ||
              hotel.address.toLowerCase().contains(query.toLowerCase()))
          .toList();

      debugPrint(
          'HotelController: Found ${searchResults.length} hotels for query: $query');
      return searchResults;
    } catch (e) {
      debugPrint('HotelController: Error searching hotels: $e');
      return [];
    }
  }

  // Fetch rooms for a hotel
  Future<void> fetchRooms(String hotelId) async {
    try {
      isLoadingRooms.value = true;

      final querySnapshot = await _firestore
          .collection('rooms')
          .where('hotelId', isEqualTo: hotelId)
          .where('status', isEqualTo: 'available')
          .get();

      rooms.value = querySnapshot.docs
          .map((doc) => Room.fromMap({...doc.data(), 'id': doc.id}))
          .toList();

      debugPrint(
          'HotelController: Fetched ${rooms.length} rooms for hotel $hotelId');
    } catch (e) {
      debugPrint('HotelController: Error fetching rooms: $e');
    } finally {
      isLoadingRooms.value = false;
    }
  }

  // Get hotel by ID
  Future<Hotel?> getHotelById(String hotelId) async {
    try {
      final doc = await _firestore.collection('hotels').doc(hotelId).get();
      if (doc.exists) {
        return Hotel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('HotelController: Error getting hotel by ID: $e');
      return null;
    }
  }

  // Add to favorites
  Future<void> addToFavorites(String hotelId, String userId) async {
    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .doc(hotelId)
          .set({
        'hotelId': hotelId,
        'addedAt': FieldValue.serverTimestamp(),
      });

      // Refresh favorite hotels
      fetchFavoriteHotels(userId);
      debugPrint('HotelController: Added hotel $hotelId to favorites');
    } catch (e) {
      debugPrint('HotelController: Error adding to favorites: $e');
    }
  }

  // Remove from favorites
  Future<void> removeFromFavorites(String hotelId, String userId) async {
    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .doc(hotelId)
          .delete();

      // Refresh favorite hotels
      fetchFavoriteHotels(userId);
      debugPrint('HotelController: Removed hotel $hotelId from favorites');
    } catch (e) {
      debugPrint('HotelController: Error removing from favorites: $e');
    }
  }

  // Fetch favorite hotels
  Future<void> fetchFavoriteHotels(String userId) async {
    try {
      final favoritesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .get();

      final favoriteHotelIds = favoritesSnapshot.docs
          .map((doc) => doc.data()['hotelId'] as String)
          .toList();

      if (favoriteHotelIds.isNotEmpty) {
        final hotelsSnapshot = await _firestore
            .collection('hotels')
            .where(FieldPath.documentId, whereIn: favoriteHotelIds)
            .get();

        favoriteHotels.value =
            hotelsSnapshot.docs.map((doc) => Hotel.fromFirestore(doc)).toList();
      } else {
        favoriteHotels.value = [];
      }

      debugPrint(
          'HotelController: Fetched ${favoriteHotels.length} favorite hotels');
    } catch (e) {
      debugPrint('HotelController: Error fetching favorite hotels: $e');
    }
  }

  // Fetch user bookings
  Future<void> fetchUserBookings(String userId) async {
    try {
      isLoadingBookings.value = true;

      final querySnapshot = await _firestore
          .collection('bookings')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      userBookings.value = querySnapshot.docs
          .map((doc) => Booking.fromMap({...doc.data(), 'id': doc.id}))
          .toList();

      debugPrint(
          'HotelController: Fetched ${userBookings.length} bookings for user $userId');
    } catch (e) {
      debugPrint('HotelController: Error fetching user bookings: $e');
    } finally {
      isLoadingBookings.value = false;
    }
  }

  // Create booking using merged Firebase service
  Future<String?> createBooking(Booking booking) async {
    try {
      // Use merged Firebase service for better integration
      String? bookingId = await MergedAppFirebaseService.createBooking(booking);
      if (bookingId != null) {
        debugPrint('HotelController: Created booking with ID: $bookingId');
        // Refresh user bookings
        await fetchUserBookings(booking.userId);
      }
      return bookingId;
    } catch (e) {
      debugPrint('HotelController: Error creating booking: $e');
      return null;
    }
  }

  // Filter hotels by price range
  List<Hotel> filterHotelsByPrice(double minPrice, double maxPrice) {
    return hotels
        .where((hotel) => hotel.price >= minPrice && hotel.price <= maxPrice)
        .toList();
  }

  // Filter hotels by rating
  List<Hotel> filterHotelsByRating(double minRating) {
    return hotels.where((hotel) => hotel.rating >= minRating).toList();
  }

  // Clear all data
  void clearData() {
    hotels.clear();
    popularHotels.clear();
    favoriteHotels.clear();
    rooms.clear();
    userBookings.clear();
    selectedHotel.value = null;
    selectedRoom.value = null;
    error.value = null;
  }
}
